<template>
  <div class="writing">
    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
      <div>
        <nav aria-label="breadcrumb">
          <ol class="breadcrumb">
            <li class="breadcrumb-item">
              <router-link :to="`/projects/${projectId}`">{{ projectName }}</router-link>
            </li>
            <li class="breadcrumb-item active">内容创作</li>
          </ol>
        </nav>
        <h1 class="h3 mb-0">
          <i class="bi bi-pencil-square me-2"></i>
          内容创作
        </h1>
      </div>
      <div class="d-flex gap-2">
        <button
          class="btn btn-outline-success"
          @click="showCreateChapterModal = true"
        >
          <i class="bi bi-plus-circle me-2"></i>
          新建章节
        </button>
        <button
          class="btn btn-outline-primary"
          @click="showAIWriteModal = true"
          :disabled="!currentChapter"
        >
          <i class="bi bi-magic me-2"></i>
          AI续写
        </button>
      </div>
    </div>

    <div class="row">
      <!-- 章节列表 -->
      <div class="col-lg-3">
        <div class="card h-100">
          <div class="card-header">
            <h6 class="card-title mb-0">
              <i class="bi bi-list me-2"></i>
              章节列表
            </h6>
          </div>
          <div class="card-body p-0">
            <div v-if="loading" class="text-center py-4">
              <div class="spinner-border spinner-border-sm text-primary"></div>
            </div>
            <div v-else-if="chapters.length === 0" class="text-center py-4 text-muted">
              <i class="bi bi-file-plus display-6 mb-2"></i>
              <p class="small">暂无章节</p>
              <button
                class="btn btn-sm btn-primary"
                @click="showCreateChapterModal = true"
              >
                创建第一章
              </button>
            </div>
            <div v-else class="chapter-list">
              <div
                v-for="chapter in chapters"
                :key="chapter.id"
                class="chapter-item"
                :class="{ active: currentChapter?.id === chapter.id }"
                @click="selectChapter(chapter)"
              >
                <div class="chapter-info">
                  <div class="chapter-title">
                    {{ chapter.title }}
                    <i
                      v-if="chapter.vectorized"
                      class="bi bi-cpu-fill text-success ms-1"
                      title="已向量化"
                    ></i>
                    <i
                      v-else-if="isVectorizing && vectorizingChapterId === chapter.id"
                      class="bi bi-arrow-repeat text-primary ms-1 spinning"
                      title="向量化中..."
                    ></i>
                  </div>
                  <div class="chapter-meta">
                    <span class="badge bg-secondary me-1">第{{ chapter.chapter_number }}章</span>
                    <span class="text-muted small">{{ formatWords(chapter.word_count) }}字</span>
                    <span v-if="chapter.memory_count" class="badge bg-info ms-1">
                      {{ chapter.memory_count }}条记忆
                    </span>
                  </div>
                </div>
                <div class="chapter-actions">
                  <div class="dropdown">
                    <button
                      class="btn btn-sm btn-outline-secondary dropdown-toggle"
                      type="button"
                      :id="`chapter-dropdown-${chapter.id}`"
                      data-bs-toggle="dropdown"
                      aria-expanded="false"
                      @click.stop
                    >
                      <i class="bi bi-three-dots"></i>
                    </button>
                    <ul class="dropdown-menu" :aria-labelledby="`chapter-dropdown-${chapter.id}`">
                      <li>
                        <a class="dropdown-item" href="#" @click.prevent="editChapter(chapter)">
                          <i class="bi bi-pencil me-2"></i>编辑
                        </a>
                      </li>
                      <li><hr class="dropdown-divider"></li>
                      <li>
                        <a
                          class="dropdown-item"
                          href="#"
                          @click.prevent="vectorizeChapter(chapter)"
                          :class="{ 'disabled': isVectorizing }"
                        >
                          <i class="bi bi-cpu me-2" :class="{ 'text-primary': isVectorizing }"></i>
                          <span v-if="isVectorizing && vectorizingChapterId === chapter.id">
                            向量化中...
                          </span>
                          <span v-else>
                            向量化章节
                          </span>
                        </a>
                      </li>
                      <li>
                        <a class="dropdown-item" href="#" @click.prevent="viewChapterMemories(chapter)">
                          <i class="bi bi-brain me-2"></i>查看记忆
                        </a>
                      </li>
                      <li><hr class="dropdown-divider"></li>
                      <li>
                        <a class="dropdown-item text-danger" href="#" @click.prevent="deleteChapter(chapter)">
                          <i class="bi bi-trash me-2"></i>删除
                        </a>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 编辑器 -->
      <div class="col-lg-6">
        <!-- AI写作助手 -->
        <AIWritingAssistant
          v-if="currentChapter"
          :project-id="projectId"
          :current-content="currentChapter.content"
          @content-generated="onAIContentGenerated"
          @suggestion-applied="onSuggestionApplied"
        />

        <div v-if="!currentChapter" class="card h-100">
          <div class="card-body d-flex align-items-center justify-content-center">
            <div class="text-center text-muted">
              <i class="bi bi-file-text display-1 mb-3"></i>
              <h5>选择一个章节开始创作</h5>
              <p>从左侧选择章节，或创建新章节开始写作</p>
            </div>
          </div>
        </div>

        <div v-else class="card h-100">
          <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <h6 class="card-title mb-1">{{ currentChapter.title }}</h6>
                <div class="text-muted small">
                  第{{ currentChapter.chapter_number }}章 ·
                  {{ formatWords(currentChapter.word_count) }}字 ·
                  {{ currentChapter.status === 'draft' ? '草稿' : '已完成' }}
                </div>
              </div>
              <div class="d-flex gap-2">
                <!-- 编辑器工具 -->
                <div class="btn-group" role="group">
                  <button
                    class="btn btn-sm btn-outline-secondary"
                    @click="toggleFullscreen"
                    title="全屏编辑"
                  >
                    <i :class="isFullscreen ? 'bi bi-fullscreen-exit' : 'bi bi-fullscreen'"></i>
                  </button>
                  <button
                    class="btn btn-sm btn-outline-secondary"
                    @click="toggleFocusMode"
                    :class="{ active: focusMode }"
                    title="专注模式"
                  >
                    <i class="bi bi-eye-slash"></i>
                  </button>
                  <button
                    class="btn btn-sm btn-outline-secondary"
                    @click="togglePreview"
                    title="预览模式"
                  >
                    <i :class="showPreview ? 'bi bi-pencil' : 'bi bi-eye'"></i>
                    {{ showPreview ? '编辑' : '预览' }}
                  </button>
                </div>

                <!-- 保存状态 -->
                <div class="save-status">
                  <span v-if="autoSaving" class="text-muted small">
                    <span class="spinner-border spinner-border-sm me-1"></span>
                    自动保存中...
                  </span>
                  <span v-else-if="lastSaved" class="text-muted small">
                    <i class="bi bi-check-circle text-success me-1"></i>
                    {{ formatLastSaved() }}
                  </span>
                </div>

                <button
                  class="btn btn-sm btn-primary"
                  @click="saveChapter"
                  :disabled="saving || !hasChanges"
                >
                  <span v-if="saving" class="spinner-border spinner-border-sm me-1"></span>
                  <i v-else class="bi bi-save me-1"></i>
                  {{ saving ? '保存中...' : '保存' }}
                </button>
              </div>
            </div>
          </div>

          <div class="card-body p-0" :class="{ 'fullscreen-editor': isFullscreen, 'focus-mode': focusMode }">
            <!-- 编辑模式 -->
            <div v-if="!showPreview" class="editor-container">
              <!-- 编辑器工具栏 -->
              <div class="editor-toolbar" v-if="!focusMode">
                <div class="toolbar-left">
                  <div class="word-count-live">
                    <span class="count-number">{{ currentChapter.content?.length || 0 }}</span>
                    <span class="count-label">字</span>
                  </div>
                  <div class="cursor-position" v-if="cursorPosition.line">
                    第 {{ cursorPosition.line }} 行，第 {{ cursorPosition.column }} 列
                  </div>
                </div>
                <div class="toolbar-right">
                  <button
                    class="btn btn-sm btn-outline-secondary"
                    @click="insertTimestamp"
                    title="插入时间戳"
                  >
                    <i class="bi bi-clock"></i>
                  </button>
                  <button
                    class="btn btn-sm btn-outline-secondary"
                    @click="showFindReplace = !showFindReplace"
                    title="查找替换"
                  >
                    <i class="bi bi-search"></i>
                  </button>
                  <button
                    class="btn btn-sm btn-outline-secondary"
                    @click="formatText"
                    title="格式化文本"
                  >
                    <i class="bi bi-text-paragraph"></i>
                  </button>
                </div>
              </div>

              <!-- 查找替换面板 -->
              <div v-if="showFindReplace" class="find-replace-panel">
                <div class="find-replace-controls">
                  <div class="input-group input-group-sm">
                    <input
                      v-model="findText"
                      type="text"
                      class="form-control"
                      placeholder="查找..."
                      @keyup.enter="findNext"
                    >
                    <button class="btn btn-outline-secondary" @click="findNext">
                      <i class="bi bi-search"></i>
                    </button>
                  </div>
                  <div class="input-group input-group-sm">
                    <input
                      v-model="replaceText"
                      type="text"
                      class="form-control"
                      placeholder="替换为..."
                      @keyup.enter="replaceNext"
                    >
                    <button class="btn btn-outline-secondary" @click="replaceNext">
                      替换
                    </button>
                    <button class="btn btn-outline-secondary" @click="replaceAll">
                      全部替换
                    </button>
                  </div>
                  <button class="btn btn-sm btn-outline-secondary" @click="showFindReplace = false">
                    <i class="bi bi-x"></i>
                  </button>
                </div>
              </div>

              <!-- 主编辑器 -->
              <textarea
                ref="editorTextarea"
                v-model="currentChapter.content"
                class="form-control editor-textarea"
                :class="{ 'focus-mode-textarea': focusMode }"
                placeholder="开始写作..."
                @input="handleEditorInput"
                @keydown="handleKeydown"
                @select="updateCursorPosition"
                @click="updateCursorPosition"
                spellcheck="false"
              ></textarea>

              <!-- 编辑器底部状态栏 -->
              <div class="editor-status-bar" v-if="!focusMode">
                <div class="status-left">
                  <span class="status-item">
                    <i class="bi bi-type"></i>
                    {{ formatWords(currentChapter.content?.length || 0) }} 字
                  </span>
                  <span class="status-item">
                    <i class="bi bi-paragraph"></i>
                    {{ paragraphCount }} 段
                  </span>
                  <span class="status-item">
                    <i class="bi bi-clock"></i>
                    {{ readingTime }}
                  </span>
                </div>
                <div class="status-right">
                  <span class="status-item" :class="{ 'text-success': !hasChanges, 'text-warning': hasChanges }">
                    <i :class="hasChanges ? 'bi bi-circle-fill' : 'bi bi-check-circle'"></i>
                    {{ hasChanges ? '未保存' : '已保存' }}
                  </span>
                </div>
              </div>
            </div>

            <!-- 预览模式 -->
            <div v-else class="preview-container">
              <div class="preview-content" v-html="formattedContent"></div>
            </div>
          </div>

          <div class="card-footer">
            <div class="row text-center">
              <div class="col-3">
                <div class="stat-item">
                  <div class="stat-value">{{ currentChapter.word_count || 0 }}</div>
                  <div class="stat-label">字数</div>
                </div>
              </div>
              <div class="col-3">
                <div class="stat-item">
                  <div class="stat-value">{{ paragraphCount }}</div>
                  <div class="stat-label">段落</div>
                </div>
              </div>
              <div class="col-3">
                <div class="stat-item">
                  <div class="stat-value">{{ readingTime }}</div>
                  <div class="stat-label">阅读时间</div>
                </div>
              </div>
              <div class="col-3">
                <div class="stat-item">
                  <div class="stat-value">{{ formatDate(currentChapter.updated_at) }}</div>
                  <div class="stat-label">最后更新</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 协作功能面板 -->
      <div class="col-lg-3">
        <div class="collaboration-panel">
          <!-- 版本历史 -->
          <VersionHistory
            v-if="currentChapter"
            :project-id="projectId"
            :chapter-id="currentChapter.id"
            :current-content="currentChapter.content"
            :has-changes="hasChanges"
            @version-restored="handleVersionRestored"
            class="mb-3"
          />

          <!-- AI智能助手 -->
          <AgentChat
            v-if="currentChapter"
            :project-id="projectId"
            :chapter-id="currentChapter.id"
            @status-change="handleAgentStatusChange"
          />
        </div>
      </div>
    </div>

    <!-- 创建章节模态框 -->
    <CreateChapterModal
      v-model:show="showCreateChapterModal"
      :project-id="projectId"
      @created="handleChapterCreated"
    />

    <!-- AI续写模态框 -->
    <AIWriteModal
      v-model:show="showAIWriteModal"
      :project-id="projectId"
      :chapter="currentChapter"
      @generated="handleAIGenerated"
    />

    <!-- 向量化进度模态框 -->
    <VectorizationProgressModal
      v-model:show="showVectorizationModal"
      :current-chapter="vectorizingChapter"
      :is-processing="isVectorizing"
      :current-step="vectorizationStep"
      :extraction-results="extractionResults"
      :error-message="vectorizationError"
      @view-memories="viewChapterMemories(vectorizingChapter)"
    />
  </div>
</template>

<script>
import { ref, computed, onMounted, onUpdated, watch, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import { useProjectsStore } from '@/stores/projects'
import { useAppStore } from '@/stores/app'
import { projectApi } from '@/api/projects'
import CreateChapterModal from '@/components/modals/CreateChapterModal.vue'
import AIWriteModal from '@/components/modals/AIWriteModal.vue'
import VectorizationProgressModal from '@/components/modals/VectorizationProgressModal.vue'
import AIWritingAssistant from '@/components/ai/AIWritingAssistant.vue'
import VersionHistory from '@/components/common/VersionHistory.vue'
import AgentChat from '@/components/AgentChat.vue'

export default {
  name: 'Writing',
  components: {
    CreateChapterModal,
    AIWriteModal,
    VectorizationProgressModal,
    AIWritingAssistant,
    VersionHistory,
    AgentChat
  },
  setup() {
    const route = useRoute()
    const projectsStore = useProjectsStore()
    const appStore = useAppStore()

    const projectId = computed(() => route.params.id)
    const project = computed(() => projectsStore.currentProject)
    const projectName = computed(() => project.value?.name || '项目')

    const loading = ref(false)
    const saving = ref(false)
    const autoSaving = ref(false)
    const hasChanges = ref(false)
    const showPreview = ref(false)
    const showCreateChapterModal = ref(false)
    const showAIWriteModal = ref(false)

    // 编辑器增强功能
    const isFullscreen = ref(false)
    const focusMode = ref(false)
    const showFindReplace = ref(false)
    const findText = ref('')
    const replaceText = ref('')
    const cursorPosition = ref({ line: 0, column: 0 })
    const lastSaved = ref(null)
    const editorTextarea = ref(null)

    const chapters = ref([])
    const currentChapter = ref(null)
    const originalContent = ref('')

    // 向量化相关状态
    const isVectorizing = ref(false)
    const vectorizingChapterId = ref(null)
    const vectorizationProgress = ref(0)
    const showVectorizationModal = ref(false)
    const vectorizingChapter = ref(null)
    const vectorizationStep = ref(0)
    const extractionResults = ref([])
    const vectorizationError = ref('')

    // 自动保存定时器
    let autoSaveTimer = null

    const paragraphCount = computed(() => {
      if (!currentChapter.value?.content) return 0
      return currentChapter.value.content.split('\n\n').filter(p => p.trim()).length
    })

    const readingTime = computed(() => {
      if (!currentChapter.value?.word_count) return '0分钟'
      const minutes = Math.ceil(currentChapter.value.word_count / 300) // 假设每分钟300字
      return `${minutes}分钟`
    })

    const formattedContent = computed(() => {
      if (!currentChapter.value?.content) return ''
      return currentChapter.value.content
        .split('\n\n')
        .map(paragraph => `<p>${paragraph.trim()}</p>`)
        .join('')
    })

    const formatWords = (count) => {
      if (!count) return 0
      return new Intl.NumberFormat('zh-CN').format(count)
    }

    const formatDate = (dateString) => {
      if (!dateString) return '未知'
      return new Date(dateString).toLocaleDateString('zh-CN')
    }

    const markAsChanged = () => {
      hasChanges.value = true
      // 实时更新字数
      if (currentChapter.value) {
        currentChapter.value.word_count = currentChapter.value.content.length
      }

      // 启动自动保存
      if (autoSaveTimer) {
        clearTimeout(autoSaveTimer)
      }
      autoSaveTimer = setTimeout(() => {
        autoSaveChapter()
      }, 3000) // 3秒后自动保存
    }

    // 编辑器增强功能
    const toggleFullscreen = () => {
      isFullscreen.value = !isFullscreen.value
      if (isFullscreen.value) {
        document.documentElement.requestFullscreen?.()
      } else {
        document.exitFullscreen?.()
      }
    }

    const toggleFocusMode = () => {
      focusMode.value = !focusMode.value
    }

    const handleEditorInput = (event) => {
      markAsChanged()
      updateCursorPosition()
    }

    const handleKeydown = (event) => {
      // Ctrl+S 保存
      if (event.ctrlKey && event.key === 's') {
        event.preventDefault()
        saveChapter()
      }
      // Ctrl+F 查找
      if (event.ctrlKey && event.key === 'f') {
        event.preventDefault()
        showFindReplace.value = true
      }
      // Esc 退出专注模式
      if (event.key === 'Escape' && focusMode.value) {
        focusMode.value = false
      }
    }

    const updateCursorPosition = () => {
      if (!editorTextarea.value) return

      const textarea = editorTextarea.value
      const text = textarea.value
      const cursorPos = textarea.selectionStart

      const lines = text.substring(0, cursorPos).split('\n')
      cursorPosition.value = {
        line: lines.length,
        column: lines[lines.length - 1].length + 1
      }
    }

    const insertTimestamp = () => {
      if (!editorTextarea.value) return

      const timestamp = new Date().toLocaleString('zh-CN')
      const textarea = editorTextarea.value
      const start = textarea.selectionStart
      const end = textarea.selectionEnd
      const text = textarea.value

      const newText = text.substring(0, start) + `[${timestamp}] ` + text.substring(end)
      currentChapter.value.content = newText
      markAsChanged()

      // 设置光标位置
      setTimeout(() => {
        textarea.selectionStart = textarea.selectionEnd = start + timestamp.length + 3
        textarea.focus()
      }, 0)
    }

    const formatText = () => {
      if (!currentChapter.value?.content) return

      // 简单的文本格式化：统一段落间距
      let formatted = currentChapter.value.content
        .replace(/\n{3,}/g, '\n\n') // 多个换行符替换为两个
        .replace(/[ \t]+/g, ' ') // 多个空格替换为一个
        .trim()

      currentChapter.value.content = formatted
      markAsChanged()
      appStore.showSuccess('文本格式化完成')
    }

    const findNext = () => {
      if (!findText.value || !editorTextarea.value) return

      const textarea = editorTextarea.value
      const text = textarea.value
      const searchStart = textarea.selectionEnd
      const index = text.indexOf(findText.value, searchStart)

      if (index !== -1) {
        textarea.setSelectionRange(index, index + findText.value.length)
        textarea.focus()
      } else {
        // 从头开始查找
        const indexFromStart = text.indexOf(findText.value)
        if (indexFromStart !== -1) {
          textarea.setSelectionRange(indexFromStart, indexFromStart + findText.value.length)
          textarea.focus()
        } else {
          appStore.showInfo('未找到匹配内容')
        }
      }
    }

    const replaceNext = () => {
      if (!findText.value || !editorTextarea.value) return

      const textarea = editorTextarea.value
      const selectedText = textarea.value.substring(textarea.selectionStart, textarea.selectionEnd)

      if (selectedText === findText.value) {
        // 替换当前选中的文本
        const start = textarea.selectionStart
        const end = textarea.selectionEnd
        const text = textarea.value

        currentChapter.value.content = text.substring(0, start) + replaceText.value + text.substring(end)
        markAsChanged()

        // 继续查找下一个
        setTimeout(() => {
          textarea.selectionStart = textarea.selectionEnd = start + replaceText.value.length
          findNext()
        }, 0)
      } else {
        findNext()
      }
    }

    const replaceAll = () => {
      if (!findText.value || !currentChapter.value?.content) return

      const originalContent = currentChapter.value.content
      const newContent = originalContent.replace(new RegExp(findText.value, 'g'), replaceText.value)

      if (newContent !== originalContent) {
        currentChapter.value.content = newContent
        markAsChanged()
        const count = (originalContent.match(new RegExp(findText.value, 'g')) || []).length
        appStore.showSuccess(`已替换 ${count} 处内容`)
      } else {
        appStore.showInfo('未找到匹配内容')
      }
    }

    const formatLastSaved = () => {
      if (!lastSaved.value) return ''
      const now = new Date()
      const saved = new Date(lastSaved.value)
      const diff = Math.floor((now - saved) / 1000)

      if (diff < 60) return '刚刚保存'
      if (diff < 3600) return `${Math.floor(diff / 60)}分钟前保存`
      return saved.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
    }

    const autoSaveChapter = async () => {
      if (!currentChapter.value || !hasChanges.value || saving.value) return

      try {
        autoSaving.value = true
        await saveChapter(true) // 静默保存
      } catch (error) {
        console.error('自动保存失败:', error)
      } finally {
        autoSaving.value = false
      }
    }

    const loadChapters = async () => {
      try {
        loading.value = true
        const response = await projectApi.getChapters(projectId.value)
        chapters.value = response.data || []

        // 如果有章节且当前没有选中章节，自动选择第一个章节
        if (chapters.value.length > 0 && !currentChapter.value) {
          await selectChapter(chapters.value[0])
        }
      } catch (error) {
        console.error('加载章节列表失败:', error)
        appStore.showError('加载章节列表失败')
      } finally {
        loading.value = false
      }
    }

    const selectChapter = async (chapter) => {
      if (hasChanges.value && !confirm('当前章节有未保存的更改，确定要切换吗？')) {
        return
      }

      try {
        loading.value = true

        // 获取章节的完整内容
        const response = await projectApi.getChapter(projectId.value, chapter.id)
        const fullChapter = response.data

        currentChapter.value = { ...fullChapter }
        originalContent.value = fullChapter.content || ''
        hasChanges.value = false
        showPreview.value = false

        console.log('章节加载成功:', {
          title: fullChapter.title,
          contentLength: fullChapter.content?.length || 0,
          wordCount: fullChapter.word_count
        })
      } catch (error) {
        console.error('加载章节内容失败:', error)
        appStore.showError('加载章节内容失败')
      } finally {
        loading.value = false
      }
    }

    const saveChapter = async (silent = false) => {
      if (!currentChapter.value) return

      try {
        saving.value = true
        const response = await projectApi.updateChapter(
          projectId.value,
          currentChapter.value.id,
          {
            title: currentChapter.value.title,
            content: currentChapter.value.content,
            word_count: currentChapter.value.content.length
          }
        )

        // 更新本地数据
        const index = chapters.value.findIndex(c => c.id === currentChapter.value.id)
        if (index > -1) {
          chapters.value[index] = { ...response.data }
        }

        originalContent.value = currentChapter.value.content
        hasChanges.value = false
        lastSaved.value = new Date().toISOString()

        if (!silent) {
          appStore.showSuccess('章节保存成功')
        }

      } catch (error) {
        console.error('保存章节失败:', error)
        if (!silent) {
          appStore.showError('保存章节失败')
        }
      } finally {
        saving.value = false
      }
    }

    const togglePreview = () => {
      showPreview.value = !showPreview.value
    }

    const editChapter = (chapter) => {
      // TODO: 实现章节编辑功能
      console.log('编辑章节:', chapter)
    }

    const deleteChapter = async (chapter) => {
      if (!confirm(`确定要删除章节"${chapter.title}"吗？此操作不可恢复。`)) {
        return
      }

      try {
        await projectApi.deleteChapter(projectId.value, chapter.id)
        chapters.value = chapters.value.filter(c => c.id !== chapter.id)

        if (currentChapter.value?.id === chapter.id) {
          currentChapter.value = null
          hasChanges.value = false
        }

        appStore.showSuccess('章节删除成功')
      } catch (error) {
        console.error('删除章节失败:', error)
        appStore.showError('删除章节失败')
      }
    }

    // 向量化章节
    const vectorizeChapter = async (chapter) => {
      if (isVectorizing.value) {
        appStore.showWarning('正在处理其他章节，请稍候')
        return
      }

      try {
        // 初始化状态
        isVectorizing.value = true
        vectorizingChapterId.value = chapter.id
        vectorizingChapter.value = chapter
        vectorizationStep.value = 0
        extractionResults.value = []
        vectorizationError.value = ''
        showVectorizationModal.value = true

        // 步骤1: 获取章节完整内容
        vectorizationStep.value = 0
        appStore.showInfo('正在获取章节内容...')

        const chapterResponse = await projectApi.getChapter(projectId.value, chapter.id)
        const fullChapter = chapterResponse.data

        if (!fullChapter.content || fullChapter.content.trim().length === 0) {
          throw new Error('章节内容为空，无法进行向量化')
        }

        console.log(`章节内容长度: ${fullChapter.content.length} 字符`)
        await new Promise(resolve => setTimeout(resolve, 800))

        // 步骤2: 内容分析
        vectorizationStep.value = 1
        appStore.showInfo('正在分析章节内容...')
        await new Promise(resolve => setTimeout(resolve, 1000))

        // 步骤3: 信息提取
        vectorizationStep.value = 2
        appStore.showInfo('正在提取关键信息...')
        await new Promise(resolve => setTimeout(resolve, 500))

        // 调用向量化API
        const response = await fetch(`/api/v1/projects/${projectId.value}/chapters/${chapter.id}/process-memories`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            content: fullChapter.content,
            extract_types: ['character', 'scene', 'plot', 'world']
          })
        })

        if (!response.ok) {
          throw new Error(`向量化失败: ${response.status}`)
        }

        const result = await response.json()

        if (result.success) {
          // 步骤4: 向量编码
          vectorizationStep.value = 3
          extractionResults.value = result.extracted_memories || []
          appStore.showInfo('正在生成向量表示...')
          await new Promise(resolve => setTimeout(resolve, 1000))

          // 步骤5: 存储索引
          vectorizationStep.value = 4
          appStore.showInfo('正在保存到向量数据库...')
          await new Promise(resolve => setTimeout(resolve, 800))

          // 完成所有步骤
          vectorizationStep.value = 5
          appStore.showInfo('正在更新关联信息...')
          await new Promise(resolve => setTimeout(resolve, 500))

          // 更新章节状态
          const chapterIndex = chapters.value.findIndex(c => c.id === chapter.id)
          if (chapterIndex !== -1) {
            chapters.value[chapterIndex] = {
              ...chapters.value[chapterIndex],
              vectorized: true,
              memory_count: result.extracted_memories?.length || 0,
              last_vectorized: new Date().toISOString()
            }
          }

          // 如果是当前章节，也更新当前章节数据
          if (currentChapter.value?.id === chapter.id) {
            currentChapter.value = {
              ...currentChapter.value,
              vectorized: true,
              memory_count: result.extracted_memories?.length || 0,
              last_vectorized: new Date().toISOString()
            }
          }

          appStore.showSuccess(
            `章节"${chapter.title}"向量化完成！提取了 ${result.extracted_memories?.length || 0} 条记忆`
          )

          // 触发角色信息更新
          await refreshCharacterData()

        } else {
          throw new Error(result.message || '向量化处理失败')
        }

      } catch (error) {
        console.error('向量化章节失败:', error)
        vectorizationError.value = error.message
        appStore.showError(`向量化失败: ${error.message}`)
      } finally {
        isVectorizing.value = false
        vectorizingChapterId.value = null

        // 延迟关闭模态框，让用户看到结果
        setTimeout(() => {
          if (!vectorizationError.value) {
            showVectorizationModal.value = false
          }
        }, 2000)
      }
    }

    // 查看章节记忆
    const viewChapterMemories = async (chapter) => {
      try {
        console.log(`正在获取章节 ${chapter.title} 的记忆...`)

        // 使用正确的章节记忆API
        const response = await fetch(`/api/v1/projects/${projectId.value}/chapters/${chapter.id}/memories`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json'
          }
        })

        if (!response.ok) {
          console.error(`获取记忆失败: ${response.status} ${response.statusText}`)
          throw new Error(`获取记忆失败: ${response.status}`)
        }

        const memories = await response.json()
        console.log(`获取到 ${memories.length} 条记忆:`, memories)

        // 显示记忆信息
        if (memories.length > 0) {
          const memoryInfo = memories.map(m =>
            `• [${m.type}] ${m.title} (相似度: ${(m.similarity_score || 0).toFixed(3)})`
          ).join('\n')

          appStore.showInfo(
            `章节"${chapter.title}"包含 ${memories.length} 条记忆:\n${memoryInfo}`,
            { duration: 8000 }
          )

          // 也可以在控制台显示详细信息
          console.log('记忆详情:', memories.map(m => ({
            type: m.type,
            title: m.title,
            summary: m.summary,
            characters: m.characters,
            scenes: m.scenes
          })))
        } else {
          console.log('该章节暂无记忆数据')
          appStore.showInfo(`章节"${chapter.title}"暂无记忆数据`)
        }

      } catch (error) {
        console.error('获取章节记忆失败:', error)
        appStore.showError(`获取章节记忆失败: ${error.message}`)
      }
    }

    // 刷新角色数据
    const refreshCharacterData = async () => {
      try {
        // 这里可以触发角色列表的刷新
        // 如果有全局状态管理，可以通过store来通知其他组件更新
        console.log('触发角色数据刷新')

        // 可以发送事件给父组件或使用事件总线
        // eventBus.emit('characters-updated', projectId.value)

      } catch (error) {
        console.error('刷新角色数据失败:', error)
      }
    }

    const handleChapterCreated = (chapter) => {
      chapters.value.push(chapter)
      selectChapter(chapter)
      showCreateChapterModal.value = false
      appStore.showSuccess(`章节"${chapter.title}"创建成功`)
    }

    const handleAIGenerated = (generatedContent) => {
      if (currentChapter.value) {
        currentChapter.value.content += '\n\n' + generatedContent
        markAsChanged()
        showAIWriteModal.value = false
        appStore.showSuccess('AI续写内容已添加')
      }
    }

    // AI写作助手事件处理
    const onAIContentGenerated = (result) => {
      if (currentChapter.value && result.content) {
        // 根据生成模式决定如何插入内容
        if (result.mode === 'continue') {
          // 续写模式：在当前内容后添加
          currentChapter.value.content += '\n\n' + result.content
        } else {
          // 其他模式：在光标位置插入或替换选中内容
          // 这里简化处理，直接添加到末尾
          currentChapter.value.content += '\n\n' + result.content
        }
        markAsChanged()
        appStore.showSuccess('AI生成的内容已插入编辑器')
      }
    }

    const onSuggestionApplied = (suggestion) => {
      appStore.showInfo(`已应用建议: ${suggestion.title}`)
    }

    // 协作功能事件处理
    const handleVersionRestored = (version) => {
      if (currentChapter.value) {
        currentChapter.value.content = version.content
        markAsChanged()
        appStore.showSuccess(`已恢复到版本: ${version.title}`)
      }
    }

    const handleAgentStatusChange = (status) => {
      console.log('Agent状态变化:', status)
      // 可以在这里处理Agent状态变化，比如显示通知等
      if (status === 'error') {
        appStore.showError('AI助手连接异常，请稍后重试')
      }
    }

    // 监听项目变化
    watch(() => projectId.value, (newId) => {
      if (newId) {
        loadChapters()
        currentChapter.value = null
        hasChanges.value = false
      }
    }, { immediate: true })

    // 页面离开前提醒保存
    window.addEventListener('beforeunload', (e) => {
      if (hasChanges.value) {
        e.preventDefault()
        e.returnValue = ''
      }
    })

    // 初始化Bootstrap组件
    const initializeBootstrapComponents = () => {
      nextTick(() => {
        // 初始化所有dropdown
        const dropdownElements = document.querySelectorAll('[data-bs-toggle="dropdown"]')
        dropdownElements.forEach(element => {
          if (!element._bootstrap_dropdown) {
            // 动态导入Bootstrap并初始化
            import('bootstrap/dist/js/bootstrap.bundle.min.js').then((bootstrap) => {
              element._bootstrap_dropdown = new bootstrap.Dropdown(element)
            })
          }
        })
      })
    }

    onMounted(() => {
      if (projectId.value) {
        loadChapters()
      }
      initializeBootstrapComponents()
    })

    onUpdated(() => {
      initializeBootstrapComponents()
    })

    return {
      projectId,
      projectName,
      loading,
      saving,
      autoSaving,
      hasChanges,
      showPreview,
      showCreateChapterModal,
      showAIWriteModal,
      // 编辑器增强
      isFullscreen,
      focusMode,
      showFindReplace,
      findText,
      replaceText,
      cursorPosition,
      lastSaved,
      editorTextarea,
      // 数据
      chapters,
      currentChapter,
      paragraphCount,
      readingTime,
      formattedContent,
      // 向量化相关
      isVectorizing,
      vectorizingChapterId,
      vectorizationProgress,
      showVectorizationModal,
      vectorizingChapter,
      vectorizationStep,
      extractionResults,
      vectorizationError,
      // 方法
      formatWords,
      formatDate,
      markAsChanged,
      selectChapter,
      saveChapter,
      togglePreview,
      editChapter,
      deleteChapter,
      vectorizeChapter,
      viewChapterMemories,
      refreshCharacterData,
      handleChapterCreated,
      handleAIGenerated,
      onAIContentGenerated,
      onSuggestionApplied,
      // 编辑器方法
      toggleFullscreen,
      toggleFocusMode,
      handleEditorInput,
      handleKeydown,
      updateCursorPosition,
      insertTimestamp,
      formatText,
      findNext,
      replaceNext,
      replaceAll,
      formatLastSaved,
      // 版本和Agent功能方法
      handleVersionRestored,
      handleAgentStatusChange
    }
  }
}
</script>

<style lang="scss" scoped>
.writing {
  padding: 1.5rem;
  height: calc(100vh - 120px);
}

.breadcrumb {
  margin-bottom: 0.5rem;

  a {
    color: #6c757d;
    text-decoration: none;

    &:hover {
      color: #495057;
    }
  }
}

.chapter-list {
  max-height: calc(100vh - 300px);
  overflow-y: auto;
}

.chapter-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid #eee;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: #f8f9fa;
  }

  &.active {
    background-color: #e3f2fd;
    border-left: 4px solid #2196f3;
  }

  &:last-child {
    border-bottom: none;
  }
}

.chapter-info {
  flex: 1;
  min-width: 0;
}

.chapter-title {
  font-weight: 500;
  margin-bottom: 0.25rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.chapter-meta {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.chapter-actions {
  flex-shrink: 0;
}

/* 编辑器容器 */
.editor-container {
  height: calc(100vh - 400px);
  min-height: 400px;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* 全屏模式 */
.fullscreen-editor {
  position: fixed !important;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background: white;

  .editor-container {
    height: 100vh;
  }
}

/* 专注模式 */
.focus-mode {
  .editor-toolbar,
  .editor-status-bar {
    display: none;
  }

  .editor-textarea {
    border: none !important;
    box-shadow: none !important;
  }
}

/* 编辑器工具栏 */
.editor-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  font-size: 0.875rem;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.toolbar-right {
  display: flex;
  gap: 0.5rem;
}

.word-count-live {
  display: flex;
  align-items: baseline;
  gap: 0.25rem;
  font-weight: 500;
  color: #374151;
}

.count-number {
  font-size: 1.1rem;
  font-weight: 600;
  color: #6366f1;
}

.count-label {
  font-size: 0.8rem;
  color: #6b7280;
}

.cursor-position {
  color: #6b7280;
  font-size: 0.8rem;
}

/* 查找替换面板 */
.find-replace-panel {
  background: #f1f5f9;
  border-bottom: 1px solid #e2e8f0;
  padding: 0.75rem 1rem;
}

.find-replace-controls {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.find-replace-controls .input-group {
  max-width: 200px;
}

/* 向量化相关样式 */
.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.chapter-item .chapter-title i {
  font-size: 0.8rem;
}

/* 主编辑器 */
.editor-textarea {
  flex: 1;
  width: 100%;
  border: none;
  resize: none;
  padding: 1.5rem;
  font-family: 'Georgia', serif;
  font-size: 1rem;
  line-height: 1.8;
  background: white;
  transition: all 0.3s ease;

  &:focus {
    outline: none;
    box-shadow: none;
  }

  &.focus-mode-textarea {
    padding: 3rem;
    font-size: 1.1rem;
    line-height: 2;
    background: #fefefe;
  }
}

/* 编辑器状态栏 */
.editor-status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 1rem;
  background: #f8fafc;
  border-top: 1px solid #e2e8f0;
  font-size: 0.8rem;
  color: #6b7280;
}

.status-left,
.status-right {
  display: flex;
  gap: 1rem;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

/* 保存状态 */
.save-status {
  display: flex;
  align-items: center;
  font-size: 0.8rem;
}

.preview-container {
  height: calc(100vh - 400px);
  min-height: 400px;
  overflow-y: auto;
  padding: 1.5rem;
}

.preview-content {
  font-family: 'Georgia', serif;
  font-size: 1rem;
  line-height: 1.8;
  color: #333;

  p {
    margin-bottom: 1.5rem;
    text-indent: 2em;
  }
}

.stat-item {
  .stat-value {
    font-size: 1.25rem;
    font-weight: 600;
    color: #495057;
  }

  .stat-label {
    font-size: 0.75rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }
}

/* 协作面板 */
.collaboration-panel {
  position: sticky;
  top: 1rem;
  max-height: calc(100vh - 2rem);
  overflow-y: auto;
}

.collaboration-panel::-webkit-scrollbar {
  width: 4px;
}

.collaboration-panel::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 2px;
}

.collaboration-panel::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 2px;
}

.collaboration-panel::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

.card {
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {
  .writing {
    padding: 1rem;
  }

  .d-flex.gap-2 {
    flex-direction: column;
    gap: 0.5rem !important;
  }

  .editor-container,
  .preview-container {
    height: 300px;
    min-height: 300px;
  }
}
</style>
