"""
基于大模型的智能文本结构化解析器
使用项目配置的AI模型进行精确的文本分析，替换简单的正则表达式匹配
"""

import json
import logging
import re
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
import requests

from app.services.ai_model_service import ai_model_service
from app.services.text_structure_analyzer import (
    Character, Scene, Dialogue, PlotEvent, EmotionalArc, StructuredChapter
)
from app.services.mock_llm_analyzer import get_mock_llm_analyzer
from app.services.optimized_llm_analyzer import get_optimized_llm_analyzer

logger = logging.getLogger(__name__)

@dataclass
class LLMAnalysisPrompt:
    """大模型分析提示词"""
    task: str
    content: str
    format_instruction: str
    examples: str = ""

class LLMTextAnalyzer:
    """基于大模型的文本分析器"""
    
    def __init__(self, model_type: str = "ollama", model_name: str = "qwen3:4b"):
        self.logger = logging.getLogger(__name__)
        self.model_type = model_type
        self.model_name = model_name
        
        # 初始化分析提示词模板
        self._init_analysis_prompts()
    
    def _init_analysis_prompts(self):
        """初始化分析提示词模板"""
        self.prompts = {
            "character_extraction": {
                "task": "从小说文本中提取详细的角色信息",
                "format_instruction": """
请严格按照以下JSON格式输出，不要添加任何其他文字：
{
  "characters": [
    {
      "name": "角色姓名",
      "gender": "male/female/unknown",
      "age": "年龄描述或unknown",
      "personality_traits": ["性格特征1", "性格特征2", "性格特征3"],
      "abilities": ["能力1", "能力2"],
      "specialties": ["特长1", "特长2"],
      "skills": ["技能1", "技能2"],
      "occupation": "职业或身份",
      "background": "背景描述",
      "appearance": "外貌描述",
      "description": "角色综合描述",
      "relationships": [
        {
          "target": "关联角色名",
          "relation": "关系类型",
          "description": "关系描述"
        }
      ],
      "importance": "main/secondary/minor",
      "first_appearance": "首次出现的章节或场景"
    }
  ]
}
""",
                "examples": """
示例输入：李明是一个年轻勇敢的剑客，擅长华山剑法，身材高大，眼神坚毅。他的师父张无忌是武林中德高望重的高手，精通内功心法，曾经是明教教主。
示例输出：
{
  "characters": [
    {
      "name": "李明",
      "gender": "male",
      "age": "年轻",
      "personality_traits": ["勇敢", "坚毅", "好学"],
      "abilities": ["剑法", "武功"],
      "specialties": ["华山剑法"],
      "skills": ["剑术", "轻功"],
      "occupation": "剑客",
      "background": "华山派弟子",
      "appearance": "身材高大，眼神坚毅",
      "description": "年轻勇敢的剑客，擅长华山剑法",
      "relationships": [
        {
          "target": "张无忌",
          "relation": "师徒",
          "description": "张无忌的弟子"
        }
      ],
      "importance": "main",
      "first_appearance": "当前章节"
    },
    {
      "name": "张无忌",
      "gender": "male",
      "age": "中年",
      "personality_traits": ["德高望重", "武功高强", "慈祥"],
      "abilities": ["内功心法", "武林绝学"],
      "specialties": ["内功", "领导能力"],
      "skills": ["内功", "剑法", "拳法"],
      "occupation": "武林高手",
      "background": "前明教教主",
      "appearance": "unknown",
      "description": "武林中德高望重的高手，精通内功心法",
      "relationships": [
        {
          "target": "李明",
          "relation": "师徒",
          "description": "李明的师父"
        }
      ],
      "importance": "secondary",
      "first_appearance": "当前章节"
    }
  ]
}
"""
            },
            
            "scene_extraction": {
                "task": "从小说文本中提取场景信息",
                "format_instruction": """
请严格按照以下JSON格式输出，不要添加任何其他文字：
{
  "scenes": [
    {
      "name": "场景名称",
      "location": "地点",
      "time": "时间或unknown",
      "weather": "天气或unknown", 
      "atmosphere": "氛围描述",
      "description": "场景描述",
      "characters_present": ["出现的角色"]
    }
  ]
}
""",
                "examples": """
示例输入：在古老的道观里，李明遇到了师父张无忌。夜幕降临，月光洒在庭院中。
示例输出：
{
  "scenes": [
    {
      "name": "道观相遇",
      "location": "古老的道观",
      "time": "夜晚",
      "weather": "unknown",
      "atmosphere": "宁静神秘",
      "description": "古老的道观，月光洒在庭院中",
      "characters_present": ["李明", "张无忌"]
    }
  ]
}
"""
            },
            
            "dialogue_extraction": {
                "task": "从小说文本中提取对话信息",
                "format_instruction": """
请严格按照以下JSON格式输出，不要添加任何其他文字：
{
  "dialogues": [
    {
      "speaker": "说话者姓名",
      "content": "对话内容",
      "emotion": "情感或unknown",
      "context": "对话上下文"
    }
  ]
}
""",
                "examples": """
示例输入："师父，我什么时候能下山？"李明问道。"当你能战胜王大龙时。"张无忌回答。
示例输出：
{
  "dialogues": [
    {
      "speaker": "李明",
      "content": "师父，我什么时候能下山？",
      "emotion": "期待",
      "context": "李明向师父询问下山时机"
    },
    {
      "speaker": "张无忌", 
      "content": "当你能战胜王大龙时。",
      "emotion": "严肃",
      "context": "师父给出下山的条件"
    }
  ]
}
"""
            },
            
            "plot_events": {
                "task": "从小说文本中提取情节事件",
                "format_instruction": """
请严格按照以下JSON格式输出，不要添加任何其他文字：
{
  "plot_events": [
    {
      "event_type": "action/conflict/resolution/revelation",
      "description": "事件描述",
      "characters_involved": ["涉及的角色"],
      "importance": "low/normal/high/critical",
      "emotional_impact": "positive/negative/neutral"
    }
  ]
}
""",
                "examples": """
示例输入：李明决定下山挑战王大龙，为民除害。最终，李明击败了王大龙。
示例输出：
{
  "plot_events": [
    {
      "event_type": "action",
      "description": "李明决定下山挑战王大龙",
      "characters_involved": ["李明", "王大龙"],
      "importance": "high",
      "emotional_impact": "positive"
    },
    {
      "event_type": "resolution",
      "description": "李明击败了王大龙",
      "characters_involved": ["李明", "王大龙"],
      "importance": "critical", 
      "emotional_impact": "positive"
    }
  ]
}
"""
            },
            
            "themes_keywords": {
                "task": "从小说文本中提取主题和关键词",
                "format_instruction": """
请严格按照以下JSON格式输出，不要添加任何其他文字：
{
  "themes": ["主题1", "主题2"],
  "keywords": ["关键词1", "关键词2", "关键词3"],
  "summary": "章节摘要"
}
""",
                "examples": """
示例输入：李明是一个年轻的剑客，他决定为民除害，挑战恶霸王大龙。
示例输出：
{
  "themes": ["正义", "成长", "勇气"],
  "keywords": ["李明", "剑客", "王大龙", "除害", "挑战"],
  "summary": "年轻剑客李明决心挑战恶霸王大龙，为民除害"
}
"""
            }
        }
    
    def analyze_chapter_with_llm(self, chapter_id: str, title: str, content: str) -> StructuredChapter:
        """使用大模型分析章节内容"""
        try:
            self.logger.info(f"开始使用大模型分析章节: {chapter_id}")

            # 使用优化的大模型分析器（分步骤进行）
            optimized_analyzer = get_optimized_llm_analyzer(self.model_type, self.model_name)
            return optimized_analyzer.analyze_chapter_with_llm(chapter_id, title, content)

        except Exception as e:
            self.logger.error(f"大模型章节分析失败: {e}")
            raise

    def _analyze_with_real_llm(self, chapter_id: str, title: str, content: str) -> StructuredChapter:
        """使用真实大模型分析"""
        # 创建结构化章节对象
        structured_chapter = StructuredChapter(
            chapter_id=chapter_id,
            title=title,
            content=content,
            word_count=len(content)
        )

        # 1. 提取角色信息
        characters_data = self._extract_with_llm("character_extraction", content)
        structured_chapter.characters = self._parse_characters(characters_data)

        # 2. 提取场景信息
        scenes_data = self._extract_with_llm("scene_extraction", content)
        structured_chapter.scenes = self._parse_scenes(scenes_data)

        # 3. 提取对话信息
        dialogues_data = self._extract_with_llm("dialogue_extraction", content)
        structured_chapter.dialogues = self._parse_dialogues(dialogues_data)

        # 4. 提取情节事件
        events_data = self._extract_with_llm("plot_events", content)
        structured_chapter.plot_events = self._parse_plot_events(events_data)

        # 5. 提取主题和关键词
        themes_data = self._extract_with_llm("themes_keywords", content)
        themes_info = self._parse_themes_keywords(themes_data)
        structured_chapter.themes = themes_info.get("themes", [])
        structured_chapter.keywords = themes_info.get("keywords", [])
        structured_chapter.summary = themes_info.get("summary", "")

        # 6. 生成情感弧线（基于对话和事件）
        structured_chapter.emotional_arcs = self._generate_emotional_arcs(
            structured_chapter.characters,
            structured_chapter.dialogues,
            structured_chapter.plot_events
        )

        self.logger.info(f"真实大模型章节分析完成: {chapter_id}")
        return structured_chapter

    def _analyze_with_smart_rules(self, chapter_id: str, title: str, content: str) -> StructuredChapter:
        """使用智能规则分析（回退方案）"""
        mock_analyzer = get_mock_llm_analyzer()
        return mock_analyzer.analyze_chapter_with_llm(chapter_id, title, content)
    
    def _extract_with_llm(self, analysis_type: str, content: str) -> Dict[str, Any]:
        """使用大模型提取特定类型的信息"""
        try:
            prompt_config = self.prompts[analysis_type]
            
            # 构建完整的提示词
            full_prompt = f"""
{prompt_config['task']}

{prompt_config['examples']}

现在请分析以下文本：
{content}

{prompt_config['format_instruction']}
"""
            
            # 调用大模型
            response = self._call_llm(full_prompt)
            
            # 解析JSON响应
            try:
                # 清理响应文本，提取JSON部分
                json_text = self._extract_json_from_response(response)
                result = json.loads(json_text)
                return result
            except json.JSONDecodeError as e:
                self.logger.warning(f"JSON解析失败，使用原始响应: {e}")
                return {"raw_response": response}
                
        except Exception as e:
            self.logger.error(f"大模型提取失败 ({analysis_type}): {e}")
            return {}
    
    def _call_llm(self, prompt: str) -> str:
        """调用大模型API"""
        try:
            if self.model_type == "ollama":
                return self._call_ollama(prompt)
            else:
                raise ValueError(f"不支持的模型类型: {self.model_type}")
        except Exception as e:
            self.logger.error(f"大模型调用失败: {e}")
            raise
    
    def _call_ollama(self, prompt: str) -> str:
        """调用Ollama API"""
        try:
            response = requests.post(
                'http://localhost:11434/api/generate',
                json={
                    'model': self.model_name,
                    'prompt': prompt,
                    'stream': False,
                    'options': {
                        'temperature': 0.1,  # 低温度确保输出稳定
                        'num_predict': 2000,
                        'top_p': 0.9
                    }
                },
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                return result.get('response', '')
            else:
                raise Exception(f"Ollama API调用失败: {response.status_code}")
                
        except Exception as e:
            self.logger.error(f"Ollama调用失败: {e}")
            raise
    
    def _extract_json_from_response(self, response: str) -> str:
        """从响应中提取JSON部分"""
        # 查找JSON块
        json_patterns = [
            r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}',  # 简单JSON匹配
            r'```json\s*(\{.*?\})\s*```',        # markdown代码块
            r'```\s*(\{.*?\})\s*```'             # 普通代码块
        ]
        
        for pattern in json_patterns:
            matches = re.findall(pattern, response, re.DOTALL)
            if matches:
                return matches[0] if isinstance(matches[0], str) else matches[0]
        
        # 如果没有找到明确的JSON块，尝试提取大括号内容
        start = response.find('{')
        if start != -1:
            # 找到匹配的结束大括号
            brace_count = 0
            for i, char in enumerate(response[start:], start):
                if char == '{':
                    brace_count += 1
                elif char == '}':
                    brace_count -= 1
                    if brace_count == 0:
                        return response[start:i+1]
        
        # 如果都失败了，返回原始响应
        return response
    
    def _parse_characters(self, data: Dict[str, Any]) -> List[Character]:
        """解析角色数据"""
        characters = []

        if "characters" in data:
            for char_data in data["characters"]:
                try:
                    # 处理关系数据，兼容旧格式
                    relationships = char_data.get("relationships", [])
                    if relationships and isinstance(relationships[0], str):
                        # 旧格式：字符串列表，转换为新格式
                        relationships = [
                            {"target": "unknown", "relation": rel, "description": rel}
                            for rel in relationships
                        ]
                    elif not relationships:
                        relationships = []

                    character = Character(
                        name=char_data.get("name", ""),
                        gender=char_data.get("gender"),
                        age=char_data.get("age"),
                        personality_traits=char_data.get("personality_traits", []),
                        abilities=char_data.get("abilities", []),
                        specialties=char_data.get("specialties", []),
                        skills=char_data.get("skills", []),
                        occupation=char_data.get("occupation"),
                        background=char_data.get("background", ""),
                        appearance=char_data.get("appearance", ""),
                        description=char_data.get("description", ""),
                        relationships=relationships,
                        importance=char_data.get("importance", "minor"),
                        first_appearance=char_data.get("first_appearance")
                    )
                    characters.append(character)
                except Exception as e:
                    self.logger.warning(f"解析角色数据失败: {e}")
                    self.logger.warning(f"角色数据: {char_data}")

        return characters
    
    def _parse_scenes(self, data: Dict[str, Any]) -> List[Scene]:
        """解析场景数据"""
        scenes = []
        
        if "scenes" in data:
            for scene_data in data["scenes"]:
                try:
                    scene = Scene(
                        name=scene_data.get("name", ""),
                        location=scene_data.get("location", ""),
                        time=scene_data.get("time"),
                        weather=scene_data.get("weather"),
                        atmosphere=scene_data.get("atmosphere"),
                        description=scene_data.get("description", ""),
                        characters_present=scene_data.get("characters_present", [])
                    )
                    scenes.append(scene)
                except Exception as e:
                    self.logger.warning(f"解析场景数据失败: {e}")
        
        return scenes
    
    def _parse_dialogues(self, data: Dict[str, Any]) -> List[Dialogue]:
        """解析对话数据"""
        dialogues = []
        
        if "dialogues" in data:
            for i, dialogue_data in enumerate(data["dialogues"]):
                try:
                    dialogue = Dialogue(
                        speaker=dialogue_data.get("speaker", ""),
                        content=dialogue_data.get("content", ""),
                        emotion=dialogue_data.get("emotion"),
                        context=dialogue_data.get("context", ""),
                        position=i
                    )
                    dialogues.append(dialogue)
                except Exception as e:
                    self.logger.warning(f"解析对话数据失败: {e}")
        
        return dialogues
    
    def _parse_plot_events(self, data: Dict[str, Any]) -> List[PlotEvent]:
        """解析情节事件数据"""
        events = []
        
        if "plot_events" in data:
            for event_data in data["plot_events"]:
                try:
                    event = PlotEvent(
                        event_type=event_data.get("event_type", "general"),
                        description=event_data.get("description", ""),
                        characters_involved=event_data.get("characters_involved", []),
                        importance=event_data.get("importance", "normal"),
                        emotional_impact=event_data.get("emotional_impact")
                    )
                    events.append(event)
                except Exception as e:
                    self.logger.warning(f"解析事件数据失败: {e}")
        
        return events
    
    def _parse_themes_keywords(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """解析主题和关键词数据"""
        return {
            "themes": data.get("themes", []),
            "keywords": data.get("keywords", []),
            "summary": data.get("summary", "")
        }
    
    def _generate_emotional_arcs(self, characters: List[Character], 
                               dialogues: List[Dialogue], 
                               events: List[PlotEvent]) -> List[EmotionalArc]:
        """基于角色、对话和事件生成情感弧线"""
        emotional_arcs = []
        
        # 为每个角色分析情感变化
        for character in characters:
            char_dialogues = [d for d in dialogues if d.speaker == character.name]
            char_events = [e for e in events if character.name in e.characters_involved]
            
            if char_dialogues or char_events:
                # 简单的情感弧线生成
                start_emotion = "neutral"
                end_emotion = "neutral"
                
                # 从对话中推断情感
                if char_dialogues:
                    emotions = [d.emotion for d in char_dialogues if d.emotion]
                    if emotions:
                        start_emotion = emotions[0]
                        end_emotion = emotions[-1]
                
                # 从事件中推断情感影响
                if char_events:
                    positive_events = [e for e in char_events if e.emotional_impact == "positive"]
                    negative_events = [e for e in char_events if e.emotional_impact == "negative"]
                    
                    if positive_events and not negative_events:
                        end_emotion = "positive"
                    elif negative_events and not positive_events:
                        end_emotion = "negative"
                
                if start_emotion != end_emotion:
                    arc = EmotionalArc(
                        character=character.name,
                        emotion_start=start_emotion,
                        emotion_end=end_emotion,
                        intensity=0.7,
                        triggers=[e.description for e in char_events[:2]]
                    )
                    emotional_arcs.append(arc)
        
        return emotional_arcs


# 全局实例
llm_text_analyzer = None

def get_llm_text_analyzer(model_type: str = "ollama", model_name: str = "qwen3:4b") -> LLMTextAnalyzer:
    """获取大模型文本分析器实例"""
    global llm_text_analyzer
    if llm_text_analyzer is None:
        llm_text_analyzer = LLMTextAnalyzer(model_type, model_name)
    return llm_text_analyzer
