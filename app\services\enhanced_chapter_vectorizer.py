"""
增强的章节向量化服务
集成结构化解析、知识图谱构建、多层次向量化和智能查询
"""

import logging
import json
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime
import asyncio

from app.services.text_structure_analyzer import get_text_structure_analyzer, StructuredChapter
from app.services.llm_text_analyzer import get_llm_text_analyzer
from app.services.knowledge_graph_manager import get_knowledge_graph_manager
from app.services.multi_level_vectorizer import get_multi_level_vectorizer
from app.services.intelligent_query_engine import get_intelligent_query_engine
from app.services.chapter_service import chapter_service
from app.services.ai_memory_service import get_ai_memory_service
from app.services.character_storage_service import character_storage_service

logger = logging.getLogger(__name__)

@dataclass
class VectorizationResult:
    """向量化结果"""
    chapter_id: str
    project_id: str
    success: bool
    processing_time: float
    structured_data: Optional[Dict[str, Any]] = None
    knowledge_graph_stats: Optional[Dict[str, Any]] = None
    vector_stats: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)

@dataclass
class ProjectVectorizationStats:
    """项目向量化统计"""
    project_id: str
    total_chapters: int
    vectorized_chapters: int
    total_entities: int
    total_relationships: int
    total_vectors: int
    last_updated: str
    processing_time: float

class EnhancedChapterVectorizer:
    """增强的章节向量化器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.text_analyzer = get_text_structure_analyzer()
        self.llm_analyzer = get_llm_text_analyzer()
        self.kg_manager = get_knowledge_graph_manager()
        self.vectorizer = get_multi_level_vectorizer()
        self.query_engine = get_intelligent_query_engine()
        self.ai_memory = get_ai_memory_service()
    
    async def vectorize_chapter(self, project_id: str, chapter_id: str,
                              content: str, title: str = "", use_llm: bool = True) -> VectorizationResult:
        """向量化单个章节"""
        start_time = datetime.now()
        
        try:
            self.logger.info(f"开始增强向量化章节: {chapter_id}")
            
            # 1. 结构化解析（选择使用大模型或传统方法）
            if use_llm:
                self.logger.info(f"使用大模型进行结构化解析: {chapter_id}")
                structured_chapter = self.llm_analyzer.analyze_chapter_with_llm(
                    chapter_id=chapter_id,
                    title=title,
                    content=content
                )
            else:
                self.logger.info(f"使用传统方法进行结构化解析: {chapter_id}")
                structured_chapter = self.text_analyzer.analyze_chapter(
                    chapter_id=chapter_id,
                    title=title,
                    content=content
                )
            
            # 2. 保存角色信息到数据库
            if structured_chapter.characters:
                self.logger.info(f"保存 {len(structured_chapter.characters)} 个角色到数据库")
                saved_characters = character_storage_service.save_characters(
                    project_id=project_id,
                    chapter_id=chapter_id,
                    characters=structured_chapter.characters
                )
                self.logger.info(f"成功保存 {len(saved_characters)} 个角色")

            # 3. 知识图谱构建
            kg = self.kg_manager.build_knowledge_graph(
                project_id=project_id,
                structured_chapters=[structured_chapter]
            )
            
            # 4. 多层次向量化
            vectorized_chapter = self.vectorizer.vectorize_chapter(
                project_id=project_id,
                structured_chapter=structured_chapter
            )

            # 5. 更新章节向量化状态
            processing_time = (datetime.now() - start_time).total_seconds()
            
            chapter_service.update_chapter_vectorization_status(
                project_id=project_id,
                chapter_id=chapter_id,
                memory_count=len(vectorized_chapter.semantic_blocks) + 
                           len(vectorized_chapter.paragraphs) + 
                           len(vectorized_chapter.entities),
                last_vectorized=datetime.now().isoformat()
            )
            
            # 6. 构建结果
            result = VectorizationResult(
                chapter_id=chapter_id,
                project_id=project_id,
                success=True,
                processing_time=processing_time,
                structured_data=self.text_analyzer.to_dict(structured_chapter),
                knowledge_graph_stats={
                    'entities': len(kg.entities),
                    'relationships': len(kg.relationships)
                },
                vector_stats={
                    'full_text_vectors': 1,
                    'semantic_blocks': len(vectorized_chapter.semantic_blocks),
                    'paragraphs': len(vectorized_chapter.paragraphs),
                    'entities': len(vectorized_chapter.entities),
                    'dialogues': len(vectorized_chapter.dialogues)
                }
            )
            
            self.logger.info(f"章节向量化完成: {chapter_id}, 耗时: {processing_time:.2f}秒")
            return result
            
        except Exception as e:
            processing_time = (datetime.now() - start_time).total_seconds()
            self.logger.error(f"章节向量化失败: {e}")
            
            return VectorizationResult(
                chapter_id=chapter_id,
                project_id=project_id,
                success=False,
                processing_time=processing_time,
                error_message=str(e)
            )
    
    async def vectorize_project(self, project_id: str, 
                              force_refresh: bool = False) -> ProjectVectorizationStats:
        """向量化整个项目"""
        start_time = datetime.now()
        
        try:
            self.logger.info(f"开始向量化项目: {project_id}")
            
            # 获取项目所有章节
            chapters = chapter_service.get_chapters(project_id)
            
            vectorized_count = 0
            all_structured_chapters = []
            
            # 处理每个章节
            for chapter_item in chapters:
                chapter_id = chapter_item.id
                
                # 检查是否需要重新向量化
                if not force_refresh and chapter_item.vectorized:
                    vectorized_count += 1
                    continue
                
                # 获取章节内容
                chapter = chapter_service.get_chapter(project_id, chapter_id)
                if not chapter:
                    continue
                
                # 向量化章节
                result = await self.vectorize_chapter(
                    project_id=project_id,
                    chapter_id=chapter_id,
                    content=chapter.content,
                    title=chapter.title
                )
                
                if result.success:
                    vectorized_count += 1
                    
                    # 收集结构化数据用于全局知识图谱
                    if result.structured_data:
                        structured_chapter = StructuredChapter(**result.structured_data)
                        all_structured_chapters.append(structured_chapter)
            
            # 构建全局知识图谱
            if all_structured_chapters:
                global_kg = self.kg_manager.build_knowledge_graph(
                    project_id=project_id,
                    structured_chapters=all_structured_chapters
                )
                
                total_entities = len(global_kg.entities)
                total_relationships = len(global_kg.relationships)
            else:
                total_entities = 0
                total_relationships = 0
            
            # 获取向量统计
            memory_stats = self.ai_memory.get_enhanced_memory_stats(project_id)
            total_vectors = memory_stats.get('total_memories', 0)
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            stats = ProjectVectorizationStats(
                project_id=project_id,
                total_chapters=len(chapters),
                vectorized_chapters=vectorized_count,
                total_entities=total_entities,
                total_relationships=total_relationships,
                total_vectors=total_vectors,
                last_updated=datetime.now().isoformat(),
                processing_time=processing_time
            )
            
            self.logger.info(f"项目向量化完成: {project_id}, 耗时: {processing_time:.2f}秒")
            return stats
            
        except Exception as e:
            self.logger.error(f"项目向量化失败: {e}")
            raise
    
    def get_vectorization_status(self, project_id: str) -> Dict[str, Any]:
        """获取向量化状态"""
        try:
            # 获取章节状态
            chapters = chapter_service.get_chapters(project_id)
            
            vectorized_chapters = [c for c in chapters if c.vectorized]
            total_memory_count = sum(c.memory_count for c in vectorized_chapters)
            
            # 获取知识图谱统计
            kg_stats = self.kg_manager.get_knowledge_graph_stats(project_id)
            
            # 获取记忆统计
            memory_stats = self.ai_memory.get_enhanced_memory_stats(project_id)
            
            return {
                'project_id': project_id,
                'total_chapters': len(chapters),
                'vectorized_chapters': len(vectorized_chapters),
                'vectorization_rate': len(vectorized_chapters) / len(chapters) if chapters else 0,
                'total_memories': total_memory_count,
                'knowledge_graph': kg_stats,
                'memory_stats': memory_stats,
                'last_updated': max([c.last_vectorized for c in vectorized_chapters if c.last_vectorized], default=None)
            }
            
        except Exception as e:
            self.logger.error(f"获取向量化状态失败: {e}")
            return {}
    
    def search_enhanced(self, project_id: str, query: str, 
                       search_options: Dict[str, Any] = None) -> Dict[str, Any]:
        """增强搜索"""
        try:
            if search_options is None:
                search_options = {}
            
            # 使用智能查询引擎
            result = self.query_engine.query(
                project_id=project_id,
                query_text=query
            )
            
            return {
                'query': query,
                'intent': asdict(result.intent),
                'vector_results': result.vector_results,
                'graph_results': result.graph_results,
                'synthesized_answer': result.synthesized_answer,
                'confidence': result.confidence,
                'sources': result.sources,
                'reasoning_steps': result.reasoning_steps
            }
            
        except Exception as e:
            self.logger.error(f"增强搜索失败: {e}")
            return {
                'query': query,
                'error': str(e),
                'confidence': 0.0
            }
    
    def get_query_suggestions(self, project_id: str, partial_query: str = "") -> List[str]:
        """获取查询建议"""
        try:
            return self.query_engine.get_query_suggestions(project_id, partial_query)
        except Exception as e:
            self.logger.error(f"获取查询建议失败: {e}")
            return []
    
    def analyze_chapter_structure(self, content: str, use_llm: bool = True) -> Dict[str, Any]:
        """分析章节结构（不保存）"""
        try:
            if use_llm:
                structured_chapter = self.llm_analyzer.analyze_chapter_with_llm(
                    chapter_id="temp",
                    title="临时分析",
                    content=content
                )
            else:
                structured_chapter = self.text_analyzer.analyze_chapter(
                    chapter_id="temp",
                    title="临时分析",
                    content=content
                )

            return self.text_analyzer.to_dict(structured_chapter)

        except Exception as e:
            self.logger.error(f"章节结构分析失败: {e}")
            return {}
    
    def get_project_insights(self, project_id: str) -> Dict[str, Any]:
        """获取项目洞察"""
        try:
            # 获取向量化状态
            status = self.get_vectorization_status(project_id)
            
            # 获取知识图谱统计
            kg_stats = self.kg_manager.get_knowledge_graph_stats(project_id)
            
            # 分析项目特征
            insights = {
                'vectorization_status': status,
                'knowledge_graph_analysis': kg_stats,
                'content_analysis': {
                    'total_entities': kg_stats.get('total_entities', 0),
                    'entity_diversity': len(kg_stats.get('entities', {})),
                    'relationship_complexity': len(kg_stats.get('relationships', {})),
                    'network_density': self._calculate_network_density(kg_stats)
                },
                'recommendations': self._generate_recommendations(status, kg_stats)
            }
            
            return insights
            
        except Exception as e:
            self.logger.error(f"获取项目洞察失败: {e}")
            return {}
    
    def _calculate_network_density(self, kg_stats: Dict[str, Any]) -> float:
        """计算网络密度"""
        total_entities = kg_stats.get('total_entities', 0)
        total_relationships = kg_stats.get('total_relationships', 0)
        
        if total_entities <= 1:
            return 0.0
        
        max_possible_relationships = total_entities * (total_entities - 1) / 2
        return total_relationships / max_possible_relationships if max_possible_relationships > 0 else 0.0
    
    def _generate_recommendations(self, status: Dict[str, Any], 
                                kg_stats: Dict[str, Any]) -> List[str]:
        """生成建议"""
        recommendations = []
        
        # 向量化建议
        vectorization_rate = status.get('vectorization_rate', 0)
        if vectorization_rate < 0.5:
            recommendations.append("建议完成更多章节的向量化以提高搜索效果")
        
        # 知识图谱建议
        total_entities = kg_stats.get('total_entities', 0)
        if total_entities < 10:
            recommendations.append("建议丰富角色和场景描述以构建更完整的知识图谱")
        
        # 关系网络建议
        network_density = self._calculate_network_density(kg_stats)
        if network_density < 0.1:
            recommendations.append("建议增加角色间的互动以丰富关系网络")
        
        return recommendations


# 全局实例
enhanced_chapter_vectorizer = EnhancedChapterVectorizer()


def get_enhanced_chapter_vectorizer() -> EnhancedChapterVectorizer:
    """获取增强章节向量化器实例"""
    return enhanced_chapter_vectorizer
