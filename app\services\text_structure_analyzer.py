"""
高级文本结构化解析器
用于从小说章节中提取结构化信息，包括角色、场景、情节、对话、情感等
"""

import re
import json
import logging
from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass, asdict
from datetime import datetime
import jieba
import jieba.posseg as pseg
from collections import defaultdict, Counter

# 配置jieba
jieba.setLogLevel(logging.INFO)

logger = logging.getLogger(__name__)

@dataclass
class Character:
    """角色信息"""
    name: str
    aliases: List[str] = None
    gender: Optional[str] = None
    age: Optional[str] = None
    description: str = ""
    personality_traits: List[str] = None
    abilities: List[str] = None  # 能力列表
    specialties: List[str] = None  # 特长列表
    skills: List[str] = None  # 技能列表
    occupation: Optional[str] = None  # 职业或身份
    background: str = ""  # 背景描述
    appearance: str = ""  # 外貌描述
    relationships: List[Dict[str, str]] = None  # 关系列表，包含target, relation, description
    importance: str = "minor"  # main/secondary/minor
    first_appearance: Optional[str] = None

    def __post_init__(self):
        if self.aliases is None:
            self.aliases = []
        if self.personality_traits is None:
            self.personality_traits = []
        if self.abilities is None:
            self.abilities = []
        if self.specialties is None:
            self.specialties = []
        if self.skills is None:
            self.skills = []
        if self.relationships is None:
            self.relationships = []

@dataclass
class Scene:
    """场景信息"""
    name: str
    location: str
    time: Optional[str] = None
    weather: Optional[str] = None
    atmosphere: Optional[str] = None
    description: str = ""
    characters_present: List[str] = None
    
    def __post_init__(self):
        if self.characters_present is None:
            self.characters_present = []

@dataclass
class Dialogue:
    """对话信息"""
    speaker: str
    content: str
    emotion: Optional[str] = None
    context: str = ""
    position: int = 0

@dataclass
class PlotEvent:
    """情节事件"""
    event_type: str  # action, conflict, resolution, revelation, etc.
    description: str
    characters_involved: List[str] = None
    importance: str = "normal"  # low, normal, high, critical
    emotional_impact: Optional[str] = None
    consequences: List[str] = None
    
    def __post_init__(self):
        if self.characters_involved is None:
            self.characters_involved = []
        if self.consequences is None:
            self.consequences = []

@dataclass
class EmotionalArc:
    """情感弧线"""
    character: str
    emotion_start: str
    emotion_end: str
    intensity: float  # 0.0 - 1.0
    triggers: List[str] = None
    
    def __post_init__(self):
        if self.triggers is None:
            self.triggers = []

@dataclass
class StructuredChapter:
    """结构化章节信息"""
    chapter_id: str
    title: str
    content: str
    characters: List[Character] = None
    scenes: List[Scene] = None
    dialogues: List[Dialogue] = None
    plot_events: List[PlotEvent] = None
    emotional_arcs: List[EmotionalArc] = None
    themes: List[str] = None
    keywords: List[str] = None
    summary: str = ""
    word_count: int = 0
    
    def __post_init__(self):
        if self.characters is None:
            self.characters = []
        if self.scenes is None:
            self.scenes = []
        if self.dialogues is None:
            self.dialogues = []
        if self.plot_events is None:
            self.plot_events = []
        if self.emotional_arcs is None:
            self.emotional_arcs = []
        if self.themes is None:
            self.themes = []
        if self.keywords is None:
            self.keywords = []

class TextStructureAnalyzer:
    """高级文本结构化解析器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 加载自定义词典
        self._load_custom_dict()
        
        # 初始化模式匹配规则
        self._init_patterns()
        
        # 情感词典
        self._init_emotion_dict()
        
        # 角色名称缓存
        self.character_cache = set()
        
    def _load_custom_dict(self):
        """加载自定义词典"""
        try:
            # 添加常见的小说词汇
            custom_words = [
                "修真", "仙侠", "玄幻", "都市", "言情", "悬疑",
                "主角", "配角", "反派", "男主", "女主",
                "内功", "武功", "法术", "灵力", "真气",
                "宗门", "门派", "世家", "皇室", "朝廷"
            ]
            
            for word in custom_words:
                jieba.add_word(word)
                
        except Exception as e:
            self.logger.warning(f"加载自定义词典失败: {e}")
    
    def _init_patterns(self):
        """初始化模式匹配规则"""
        # 对话模式
        self.dialogue_patterns = [
            r'"([^"]*)"',  # 双引号对话
            r'"([^"]*)"',  # 中文双引号
            r"'([^']*)'",  # 中文单引号
            r'「([^」]*)」',  # 日式引号
            r'『([^』]*)』',  # 日式书名号
        ]
        
        # 角色称呼模式
        self.character_patterns = [
            r'([^，。！？\s]{1,4})(说道?|道|说|问|答|回答|喊|叫|笑|哭|怒|惊|叹)',
            r'([^，。！？\s]{1,4})(的话|的声音|的眼神|的表情)',
            r'(老|小|大|少)([^，。！？\s]{1,3})',
            r'([^，。！？\s]{1,4})(师父|师傅|师兄|师姐|师弟|师妹)',
            r'([^，。！？\s]{1,4})(公子|小姐|夫人|先生|大人)',
        ]
        
        # 场景描述模式
        self.scene_patterns = [
            r'(在|到了?|来到|走进|进入)([^，。！？\s]{2,10})(里|中|内|上|下|前|后|旁|边)',
            r'([^，。！？\s]{2,10})(山|河|湖|海|城|镇|村|庄|宫|殿|楼|阁|院|房|屋)',
            r'(天空|大地|森林|草原|沙漠|雪山|峡谷|洞穴)',
        ]
        
        # 动作模式
        self.action_patterns = [
            r'(走|跑|飞|跳|坐|站|躺|蹲|爬|游|骑)',
            r'(打|击|攻|防|挡|躲|闪|逃|追|抓)',
            r'(看|听|闻|摸|尝|感|觉|想|思|考)',
            r'(拿|放|给|取|送|扔|抛|投|掷|射)',
        ]
        
        # 情感表达模式
        self.emotion_patterns = [
            r'(高兴|开心|快乐|兴奋|激动|喜悦|愉快)',
            r'(悲伤|难过|伤心|痛苦|绝望|沮丧|失落)',
            r'(愤怒|生气|恼火|暴怒|愤慨|恼怒|气愤)',
            r'(恐惧|害怕|惊恐|恐慌|畏惧|胆怯|紧张)',
            r'(惊讶|震惊|诧异|意外|吃惊|惊奇|惊愕)',
            r'(厌恶|讨厌|憎恨|反感|嫌弃|排斥|抵触)',
        ]
    
    def _init_emotion_dict(self):
        """初始化情感词典"""
        self.emotion_dict = {
            'positive': [
                '高兴', '开心', '快乐', '兴奋', '激动', '喜悦', '愉快', '满意', '欣慰', '幸福',
                '温暖', '甜蜜', '美好', '舒适', '轻松', '自在', '安心', '放心', '踏实', '满足'
            ],
            'negative': [
                '悲伤', '难过', '伤心', '痛苦', '绝望', '沮丧', '失落', '孤独', '寂寞', '空虚',
                '愤怒', '生气', '恼火', '暴怒', '愤慨', '恼怒', '气愤', '不满', '抱怨', '埋怨',
                '恐惧', '害怕', '惊恐', '恐慌', '畏惧', '胆怯', '紧张', '不安', '焦虑', '担心'
            ],
            'neutral': [
                '平静', '冷静', '淡然', '无所谓', '普通', '一般', '正常', '平常', '寻常', '平凡'
            ]
        }

    def analyze_chapter(self, chapter_id: str, title: str, content: str) -> StructuredChapter:
        """分析章节内容，提取结构化信息"""
        try:
            self.logger.info(f"开始分析章节: {chapter_id}")

            # 创建结构化章节对象
            structured_chapter = StructuredChapter(
                chapter_id=chapter_id,
                title=title,
                content=content,
                word_count=len(content)
            )

            # 分段处理
            paragraphs = self._split_paragraphs(content)

            # 提取各种结构化信息
            structured_chapter.characters = self._extract_characters(content, paragraphs)
            structured_chapter.scenes = self._extract_scenes(content, paragraphs)
            structured_chapter.dialogues = self._extract_dialogues(content, paragraphs)
            structured_chapter.plot_events = self._extract_plot_events(content, paragraphs)
            structured_chapter.emotional_arcs = self._extract_emotional_arcs(content, paragraphs)
            structured_chapter.themes = self._extract_themes(content)
            structured_chapter.keywords = self._extract_keywords(content)
            structured_chapter.summary = self._generate_summary(content, structured_chapter)

            self.logger.info(f"章节分析完成: {chapter_id}")
            return structured_chapter

        except Exception as e:
            self.logger.error(f"章节分析失败: {e}")
            raise

    def _split_paragraphs(self, content: str) -> List[str]:
        """分割段落"""
        # 按换行符分割，过滤空行
        paragraphs = [p.strip() for p in content.split('\n') if p.strip()]
        return paragraphs

    def _extract_characters(self, content: str, paragraphs: List[str]) -> List[Character]:
        """提取角色信息"""
        characters = {}

        # 使用正则表达式提取角色名称
        for pattern in self.character_patterns:
            matches = re.findall(pattern, content)
            for match in matches:
                if isinstance(match, tuple):
                    name = match[0]
                else:
                    name = match

                # 过滤无效名称
                if len(name) > 1 and len(name) <= 4 and name not in ['这个', '那个', '什么', '怎么']:
                    if name not in characters:
                        characters[name] = Character(
                            name=name,
                            first_appearance=content.find(name)
                        )

                    # 分析角色特征
                    self._analyze_character_traits(characters[name], content)

        # 使用词性标注进一步提取人名
        words = pseg.cut(content)
        for word, flag in words:
            if flag == 'nr' and len(word) > 1 and len(word) <= 4:  # 人名
                if word not in characters:
                    characters[word] = Character(
                        name=word,
                        first_appearance=content.find(word)
                    )
                    self._analyze_character_traits(characters[word], content)

        return list(characters.values())

    def _analyze_character_traits(self, character: Character, content: str):
        """分析角色特征"""
        # 查找角色相关的描述
        name = character.name

        # 性格特征模式
        trait_patterns = [
            f'{name}[^。！？]*?(善良|邪恶|聪明|愚蠢|勇敢|胆小|温柔|粗暴|冷静|急躁)',
            f'(善良|邪恶|聪明|愚蠢|勇敢|胆小|温柔|粗暴|冷静|急躁)[^。！？]*?{name}',
        ]

        for pattern in trait_patterns:
            matches = re.findall(pattern, content)
            for match in matches:
                if isinstance(match, tuple):
                    trait = match[0] if match[0] else match[1]
                else:
                    trait = match
                if trait and trait not in character.personality_traits:
                    character.personality_traits.append(trait)

        # 性别推断
        gender_patterns = [
            (f'{name}[^。！？]*?(他|男|先生|公子|少爷)', 'male'),
            (f'{name}[^。！？]*?(她|女|小姐|夫人|姑娘)', 'female'),
        ]

        for pattern, gender in gender_patterns:
            if re.search(pattern, content):
                character.gender = gender
                break

    def _extract_scenes(self, content: str, paragraphs: List[str]) -> List[Scene]:
        """提取场景信息"""
        scenes = []

        for i, paragraph in enumerate(paragraphs):
            # 检查是否包含场景描述
            for pattern in self.scene_patterns:
                matches = re.findall(pattern, paragraph)
                for match in matches:
                    if isinstance(match, tuple):
                        location = ''.join(match)
                    else:
                        location = match

                    # 创建场景对象
                    scene = Scene(
                        name=f"场景_{len(scenes)+1}",
                        location=location,
                        description=paragraph[:100] + "..." if len(paragraph) > 100 else paragraph
                    )

                    # 分析场景氛围
                    scene.atmosphere = self._analyze_atmosphere(paragraph)

                    # 分析时间
                    scene.time = self._extract_time(paragraph)

                    # 分析天气
                    scene.weather = self._extract_weather(paragraph)

                    scenes.append(scene)

        return scenes

    def _analyze_atmosphere(self, text: str) -> Optional[str]:
        """分析场景氛围"""
        atmosphere_keywords = {
            'peaceful': ['宁静', '安静', '平静', '祥和', '温馨'],
            'tense': ['紧张', '压抑', '沉重', '凝重', '严肃'],
            'mysterious': ['神秘', '诡异', '奇怪', '诡秘', '阴森'],
            'romantic': ['浪漫', '温馨', '甜蜜', '美好', '温柔'],
            'dangerous': ['危险', '恐怖', '可怕', '威胁', '险恶']
        }

        for atmosphere, keywords in atmosphere_keywords.items():
            if any(keyword in text for keyword in keywords):
                return atmosphere

        return None

    def _extract_time(self, text: str) -> Optional[str]:
        """提取时间信息"""
        time_patterns = [
            r'(早上|上午|中午|下午|晚上|夜里|深夜|黎明|黄昏)',
            r'(春天|夏天|秋天|冬天|春季|夏季|秋季|冬季)',
            r'(一月|二月|三月|四月|五月|六月|七月|八月|九月|十月|十一月|十二月)',
        ]

        for pattern in time_patterns:
            match = re.search(pattern, text)
            if match:
                return match.group(1)

        return None

    def _extract_weather(self, text: str) -> Optional[str]:
        """提取天气信息"""
        weather_patterns = [
            r'(晴天|阴天|雨天|雪天|多云|雾天)',
            r'(下雨|下雪|刮风|打雷|闪电)',
            r'(炎热|寒冷|温暖|凉爽|潮湿|干燥)',
        ]

        for pattern in weather_patterns:
            match = re.search(pattern, text)
            if match:
                return match.group(1)

        return None

    def _extract_dialogues(self, content: str, paragraphs: List[str]) -> List[Dialogue]:
        """提取对话信息"""
        dialogues = []

        for i, paragraph in enumerate(paragraphs):
            # 提取对话内容
            for pattern in self.dialogue_patterns:
                matches = re.finditer(pattern, paragraph)
                for match in matches:
                    dialogue_content = match.group(1)

                    # 查找说话者
                    speaker = self._identify_speaker(paragraph, match.start())

                    # 分析情感
                    emotion = self._analyze_dialogue_emotion(paragraph)

                    dialogue = Dialogue(
                        speaker=speaker or "未知",
                        content=dialogue_content,
                        emotion=emotion,
                        context=paragraph,
                        position=i
                    )

                    dialogues.append(dialogue)

        return dialogues

    def _identify_speaker(self, paragraph: str, dialogue_pos: int) -> Optional[str]:
        """识别对话说话者"""
        # 在对话前后查找说话者
        before_text = paragraph[:dialogue_pos]
        after_text = paragraph[dialogue_pos:]

        # 常见的说话者模式
        speaker_patterns = [
            r'([^，。！？\s]{1,4})(说道?|道|说|问|答|回答|喊|叫|笑|哭|怒|惊|叹)',
            r'([^，。！？\s]{1,4})(开口|说话|发声)',
        ]

        # 先在对话前查找
        for pattern in speaker_patterns:
            matches = re.findall(pattern, before_text)
            if matches:
                return matches[-1][0]  # 返回最后一个匹配的说话者

        # 再在对话后查找
        for pattern in speaker_patterns:
            matches = re.findall(pattern, after_text)
            if matches:
                return matches[0][0]  # 返回第一个匹配的说话者

        return None

    def _analyze_dialogue_emotion(self, paragraph: str) -> Optional[str]:
        """分析对话情感"""
        for emotion_type, emotions in self.emotion_dict.items():
            for emotion in emotions:
                if emotion in paragraph:
                    return emotion

        return None

    def _extract_plot_events(self, content: str, paragraphs: List[str]) -> List[PlotEvent]:
        """提取情节事件"""
        events = []

        for i, paragraph in enumerate(paragraphs):
            # 检查是否包含重要动作或事件
            event_indicators = [
                '突然', '忽然', '瞬间', '立刻', '马上', '终于', '结果',
                '发现', '意识到', '明白', '理解', '想起', '记得',
                '决定', '选择', '打算', '准备', '开始', '结束',
                '成功', '失败', '胜利', '失败', '死亡', '重生'
            ]

            if any(indicator in paragraph for indicator in event_indicators):
                # 分析事件类型
                event_type = self._classify_event_type(paragraph)

                # 提取涉及的角色
                characters_involved = self._extract_characters_from_text(paragraph)

                # 评估重要性
                importance = self._assess_event_importance(paragraph)

                event = PlotEvent(
                    event_type=event_type,
                    description=paragraph[:200] + "..." if len(paragraph) > 200 else paragraph,
                    characters_involved=characters_involved,
                    importance=importance,
                    emotional_impact=self._analyze_emotional_impact(paragraph)
                )

                events.append(event)

        return events

    def _classify_event_type(self, text: str) -> str:
        """分类事件类型"""
        event_types = {
            'action': ['打', '击', '攻', '战', '斗', '跑', '逃', '追'],
            'conflict': ['争', '吵', '怒', '恨', '敌', '对', '反'],
            'revelation': ['发现', '知道', '明白', '理解', '意识', '想起'],
            'resolution': ['解决', '完成', '结束', '成功', '胜利', '和解'],
            'emotional': ['爱', '恨', '喜', '悲', '怒', '惧', '惊']
        }

        for event_type, keywords in event_types.items():
            if any(keyword in text for keyword in keywords):
                return event_type

        return 'general'

    def _extract_characters_from_text(self, text: str) -> List[str]:
        """从文本中提取角色名称"""
        characters = []

        # 使用词性标注提取人名
        words = pseg.cut(text)
        for word, flag in words:
            if flag == 'nr' and len(word) > 1 and len(word) <= 4:
                characters.append(word)

        # 使用正则表达式提取
        for pattern in self.character_patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                if isinstance(match, tuple):
                    name = match[0]
                else:
                    name = match

                if len(name) > 1 and len(name) <= 4 and name not in characters:
                    characters.append(name)

        return characters

    def _assess_event_importance(self, text: str) -> str:
        """评估事件重要性"""
        high_importance_keywords = [
            '死亡', '杀死', '重伤', '失踪', '背叛', '揭露', '秘密',
            '决战', '最终', '关键', '重要', '危机', '转折'
        ]

        normal_importance_keywords = [
            '发现', '遇到', '离开', '到达', '开始', '结束',
            '说话', '思考', '决定', '选择'
        ]

        if any(keyword in text for keyword in high_importance_keywords):
            return 'high'
        elif any(keyword in text for keyword in normal_importance_keywords):
            return 'normal'
        else:
            return 'low'

    def _analyze_emotional_impact(self, text: str) -> Optional[str]:
        """分析情感影响"""
        for emotion_type, emotions in self.emotion_dict.items():
            for emotion in emotions:
                if emotion in text:
                    return emotion_type

        return None

    def _extract_emotional_arcs(self, content: str, paragraphs: List[str]) -> List[EmotionalArc]:
        """提取情感弧线"""
        emotional_arcs = []
        character_emotions = defaultdict(list)

        # 分析每个段落中角色的情感
        for i, paragraph in enumerate(paragraphs):
            characters = self._extract_characters_from_text(paragraph)
            emotions = self._extract_emotions_from_text(paragraph)

            for character in characters:
                for emotion in emotions:
                    character_emotions[character].append({
                        'emotion': emotion,
                        'position': i,
                        'context': paragraph
                    })

        # 构建情感弧线
        for character, emotions in character_emotions.items():
            if len(emotions) >= 2:
                # 简单的情感弧线：从第一个情感到最后一个情感
                start_emotion = emotions[0]['emotion']
                end_emotion = emotions[-1]['emotion']

                # 计算情感强度变化
                intensity = self._calculate_emotional_intensity(emotions)

                arc = EmotionalArc(
                    character=character,
                    emotion_start=start_emotion,
                    emotion_end=end_emotion,
                    intensity=intensity,
                    triggers=[e['context'][:50] + "..." for e in emotions[:3]]  # 前3个触发事件
                )

                emotional_arcs.append(arc)

        return emotional_arcs

    def _extract_emotions_from_text(self, text: str) -> List[str]:
        """从文本中提取情感词"""
        emotions = []

        for emotion_type, emotion_list in self.emotion_dict.items():
            for emotion in emotion_list:
                if emotion in text:
                    emotions.append(emotion)

        return emotions

    def _calculate_emotional_intensity(self, emotions: List[Dict]) -> float:
        """计算情感强度"""
        if not emotions:
            return 0.0

        # 简单的强度计算：基于情感词的数量和变化
        intensity_map = {
            'positive': 0.7,
            'negative': 0.8,
            'neutral': 0.3
        }

        total_intensity = 0.0
        for emotion_data in emotions:
            emotion = emotion_data['emotion']
            for emotion_type, emotion_list in self.emotion_dict.items():
                if emotion in emotion_list:
                    total_intensity += intensity_map.get(emotion_type, 0.5)
                    break

        return min(total_intensity / len(emotions), 1.0)

    def _extract_themes(self, content: str) -> List[str]:
        """提取主题"""
        themes = []

        # 主题关键词
        theme_keywords = {
            '爱情': ['爱', '恋', '情', '心', '喜欢', '爱情', '恋爱'],
            '友情': ['朋友', '友谊', '伙伴', '同伴', '兄弟', '姐妹'],
            '成长': ['成长', '学习', '进步', '提升', '突破', '成熟'],
            '冒险': ['冒险', '探索', '发现', '旅行', '历险', '寻找'],
            '正义': ['正义', '公正', '善良', '邪恶', '正邪', '道德'],
            '权力': ['权力', '地位', '统治', '控制', '支配', '权威'],
            '复仇': ['复仇', '报仇', '仇恨', '报复', '雪恨', '仇敌'],
            '救赎': ['救赎', '拯救', '解救', '挽回', '弥补', '赎罪']
        }

        for theme, keywords in theme_keywords.items():
            if any(keyword in content for keyword in keywords):
                themes.append(theme)

        return themes

    def _extract_keywords(self, content: str) -> List[str]:
        """提取关键词"""
        # 使用jieba进行分词
        words = jieba.cut(content)

        # 过滤停用词和标点符号
        stop_words = {'的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这'}

        word_freq = Counter()
        for word in words:
            if len(word) > 1 and word not in stop_words and not word.isdigit():
                word_freq[word] += 1

        # 返回频率最高的关键词
        return [word for word, freq in word_freq.most_common(20)]

    def _generate_summary(self, content: str, structured_chapter: StructuredChapter) -> str:
        """生成章节摘要"""
        # 简单的摘要生成：提取关键信息
        summary_parts = []

        # 主要角色
        if structured_chapter.characters:
            main_characters = [c.name for c in structured_chapter.characters[:3]]
            summary_parts.append(f"主要角色：{', '.join(main_characters)}")

        # 主要场景
        if structured_chapter.scenes:
            main_locations = [s.location for s in structured_chapter.scenes[:2]]
            summary_parts.append(f"主要场景：{', '.join(main_locations)}")

        # 重要事件
        if structured_chapter.plot_events:
            important_events = [e.description[:50] + "..." for e in structured_chapter.plot_events if e.importance in ['high', 'critical']]
            if important_events:
                summary_parts.append(f"重要事件：{'; '.join(important_events[:2])}")

        # 主题
        if structured_chapter.themes:
            summary_parts.append(f"主要主题：{', '.join(structured_chapter.themes[:3])}")

        return '; '.join(summary_parts) if summary_parts else "暂无摘要"

    def to_dict(self, structured_chapter: StructuredChapter) -> Dict[str, Any]:
        """将结构化章节转换为字典"""
        return {
            'chapter_id': structured_chapter.chapter_id,
            'title': structured_chapter.title,
            'word_count': structured_chapter.word_count,
            'summary': structured_chapter.summary,
            'characters': [asdict(c) for c in structured_chapter.characters],
            'scenes': [asdict(s) for s in structured_chapter.scenes],
            'dialogues': [asdict(d) for d in structured_chapter.dialogues],
            'plot_events': [asdict(e) for e in structured_chapter.plot_events],
            'emotional_arcs': [asdict(a) for a in structured_chapter.emotional_arcs],
            'themes': structured_chapter.themes,
            'keywords': structured_chapter.keywords
        }


# 全局实例
text_structure_analyzer = TextStructureAnalyzer()


def get_text_structure_analyzer() -> TextStructureAnalyzer:
    """获取文本结构分析器实例"""
    return text_structure_analyzer
