<template>
  <div 
    class="modal fade" 
    :class="{ show: show }" 
    :style="{ display: show ? 'block' : 'none' }"
    tabindex="-1"
    @click.self="$emit('update:show', false)"
  >
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">
            <i class="bi bi-cpu me-2"></i>
            章节向量化处理
          </h5>
          <button 
            type="button" 
            class="btn-close" 
            @click="$emit('update:show', false)"
            :disabled="isProcessing"
          ></button>
        </div>
        
        <div class="modal-body">
          <!-- 当前处理状态 -->
          <div v-if="currentChapter" class="current-chapter mb-4">
            <h6 class="text-primary">
              <i class="bi bi-file-text me-2"></i>
              正在处理: {{ currentChapter.title }}
            </h6>
            <p class="text-muted mb-2">
              第{{ currentChapter.chapter_number }}章 • {{ formatWords(currentChapter.word_count) }}字
            </p>
          </div>

          <!-- 处理步骤 -->
          <div class="processing-steps">
            <div 
              v-for="(step, index) in processingSteps" 
              :key="index"
              class="step-item"
              :class="{ 
                'active': currentStep === index,
                'completed': currentStep > index,
                'pending': currentStep < index
              }"
            >
              <div class="step-icon">
                <i 
                  v-if="currentStep > index" 
                  class="bi bi-check-circle-fill text-success"
                ></i>
                <i 
                  v-else-if="currentStep === index" 
                  class="bi bi-arrow-repeat spinning text-primary"
                ></i>
                <i 
                  v-else 
                  class="bi bi-circle text-muted"
                ></i>
              </div>
              <div class="step-content">
                <div class="step-title">{{ step.title }}</div>
                <div class="step-description">{{ step.description }}</div>
                <div v-if="currentStep === index && step.progress" class="step-progress">
                  <div class="progress mt-2">
                    <div 
                      class="progress-bar" 
                      :style="{ width: step.progress + '%' }"
                    ></div>
                  </div>
                  <small class="text-muted">{{ step.progress }}%</small>
                </div>
              </div>
            </div>
          </div>

          <!-- 提取结果预览 -->
          <div v-if="extractionResults.length > 0" class="extraction-results mt-4">
            <h6 class="text-success">
              <i class="bi bi-brain me-2"></i>
              提取结果 ({{ extractionResults.length }}条记忆)
            </h6>
            <div class="results-grid">
              <div 
                v-for="(result, index) in extractionResults.slice(0, 6)" 
                :key="index"
                class="result-item"
              >
                <div class="result-type">
                  <i :class="getTypeIcon(result.type)" class="me-1"></i>
                  {{ getTypeLabel(result.type) }}
                </div>
                <div class="result-title">{{ result.title }}</div>
                <div class="result-summary">{{ result.summary }}</div>
              </div>
            </div>
            <div v-if="extractionResults.length > 6" class="text-center mt-2">
              <small class="text-muted">还有 {{ extractionResults.length - 6 }} 条记忆...</small>
            </div>
          </div>

          <!-- 错误信息 -->
          <div v-if="errorMessage" class="alert alert-danger mt-3">
            <i class="bi bi-exclamation-triangle me-2"></i>
            {{ errorMessage }}
          </div>
        </div>
        
        <div class="modal-footer">
          <button 
            type="button" 
            class="btn btn-secondary" 
            @click="$emit('update:show', false)"
            :disabled="isProcessing"
          >
            {{ isProcessing ? '处理中...' : '关闭' }}
          </button>
          <button 
            v-if="!isProcessing && extractionResults.length > 0" 
            type="button" 
            class="btn btn-primary"
            @click="viewAllMemories"
          >
            查看所有记忆
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue'

export default {
  name: 'VectorizationProgressModal',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    currentChapter: {
      type: Object,
      default: null
    },
    isProcessing: {
      type: Boolean,
      default: false
    },
    currentStep: {
      type: Number,
      default: 0
    },
    extractionResults: {
      type: Array,
      default: () => []
    },
    errorMessage: {
      type: String,
      default: ''
    }
  },
  emits: ['update:show', 'view-memories'],
  setup(props, { emit }) {
    const processingSteps = ref([
      {
        title: '获取内容',
        description: '获取章节完整内容并验证',
        progress: 0
      },
      {
        title: '内容分析',
        description: '分析章节内容结构和语义',
        progress: 0
      },
      {
        title: '信息提取',
        description: '提取角色、场景、剧情等关键信息',
        progress: 0
      },
      {
        title: '向量编码',
        description: '将提取的信息转换为向量表示',
        progress: 0
      },
      {
        title: '存储索引',
        description: '保存向量数据到本地索引',
        progress: 0
      },
      {
        title: '更新关联',
        description: '更新角色关系和时间线信息',
        progress: 0
      }
    ])

    const formatWords = (count) => {
      if (!count) return 0
      return new Intl.NumberFormat('zh-CN').format(count)
    }

    const getTypeIcon = (type) => {
      const iconMap = {
        character: 'bi bi-person-fill text-primary',
        scene: 'bi bi-geo-alt-fill text-success',
        plot: 'bi bi-book-fill text-warning',
        world: 'bi bi-globe text-info'
      }
      return iconMap[type] || 'bi bi-circle-fill text-secondary'
    }

    const getTypeLabel = (type) => {
      const labelMap = {
        character: '角色',
        scene: '场景',
        plot: '剧情',
        world: '世界观'
      }
      return labelMap[type] || type
    }

    const viewAllMemories = () => {
      emit('view-memories')
    }

    // 监听处理步骤变化，更新进度
    watch(() => props.currentStep, (newStep) => {
      // 更新当前步骤的进度
      if (newStep >= 0 && newStep < processingSteps.value.length) {
        // 模拟进度更新
        const step = processingSteps.value[newStep]
        if (step.progress < 100) {
          const interval = setInterval(() => {
            step.progress += 10
            if (step.progress >= 100) {
              clearInterval(interval)
            }
          }, 200)
        }
      }
    })

    return {
      processingSteps,
      formatWords,
      getTypeIcon,
      getTypeLabel,
      viewAllMemories
    }
  }
}
</script>

<style scoped>
.processing-steps {
  margin: 1rem 0;
}

.step-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 1.5rem;
  padding: 1rem;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.step-item.active {
  background-color: #f0f9ff;
  border: 1px solid #0ea5e9;
}

.step-item.completed {
  background-color: #f0fdf4;
  border: 1px solid #22c55e;
}

.step-item.pending {
  background-color: #f8fafc;
  border: 1px solid #e2e8f0;
}

.step-icon {
  margin-right: 1rem;
  font-size: 1.2rem;
  min-width: 24px;
}

.step-content {
  flex: 1;
}

.step-title {
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.step-description {
  color: #6b7280;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

.step-progress .progress {
  height: 4px;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.results-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.result-item {
  padding: 0.75rem;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: #f9fafb;
}

.result-type {
  font-size: 0.75rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.25rem;
}

.result-title {
  font-weight: 500;
  margin-bottom: 0.25rem;
  color: #111827;
}

.result-summary {
  font-size: 0.8rem;
  color: #6b7280;
  line-height: 1.4;
}

.current-chapter {
  padding: 1rem;
  background: #f8fafc;
  border-radius: 8px;
  border-left: 4px solid #3b82f6;
}
</style>
