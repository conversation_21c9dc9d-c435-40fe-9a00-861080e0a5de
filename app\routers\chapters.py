"""
章节管理API路由
"""

from typing import List, Dict, Any
from fastapi import APIRouter, HTTPException, status
from pydantic import BaseModel

from app.models.chapter import (
    ChapterCreate, ChapterUpdate, ChapterResponse, ChapterListItem
)
from app.services.chapter_service import chapter_service
from app.services.ai_memory_service import get_ai_memory_service
from app.services.character_storage_service import character_storage_service

router = APIRouter(prefix="/projects/{project_id}/chapters", tags=["chapters"])


class VectorizeChapterRequest(BaseModel):
    """章节向量化请求"""
    content: str
    extract_types: List[str] = ["character", "scene", "plot", "world"]


class VectorizeChapterResponse(BaseModel):
    """章节向量化响应"""
    success: bool
    message: str
    extracted_memories: List[Dict[str, Any]] = []
    processing_time: float = 0.0
    memory_count: int = 0


@router.post("", response_model=ChapterResponse, status_code=status.HTTP_201_CREATED)
async def create_chapter(project_id: str, chapter_data: ChapterCreate):
    """创建章节"""
    try:
        return chapter_service.create_chapter(project_id, chapter_data)
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.get("", response_model=List[ChapterListItem])
async def get_chapters(project_id: str):
    """获取项目的所有章节"""
    try:
        return chapter_service.get_chapters(project_id)
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.get("/{chapter_id}", response_model=ChapterResponse)
async def get_chapter(project_id: str, chapter_id: str):
    """获取章节详情"""
    try:
        chapter = chapter_service.get_chapter(project_id, chapter_id)
        if not chapter:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="章节不存在")
        return chapter
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.put("/{chapter_id}", response_model=ChapterResponse)
async def update_chapter(project_id: str, chapter_id: str, chapter_data: ChapterUpdate):
    """更新章节"""
    try:
        updated_chapter = chapter_service.update_chapter(project_id, chapter_id, chapter_data)
        if not updated_chapter:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="章节不存在")
        return updated_chapter
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.delete("/{chapter_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_chapter(project_id: str, chapter_id: str):
    """删除章节"""
    try:
        success = chapter_service.delete_chapter(project_id, chapter_id)
        if not success:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="章节不存在")
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.get("/search/{query}", response_model=List[ChapterListItem])
async def search_chapters(project_id: str, query: str):
    """搜索章节"""
    try:
        return chapter_service.search_chapters(project_id, query)
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.get("/stats", response_model=dict)
async def get_chapter_stats(project_id: str):
    """获取章节统计信息"""
    try:
        return chapter_service.get_chapter_stats(project_id)
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.post("/{chapter_id}/process-memories", response_model=VectorizeChapterResponse)
async def vectorize_chapter(
    project_id: str,
    chapter_id: str,
    request: VectorizeChapterRequest
):
    """向量化章节内容，提取记忆"""
    try:
        import time
        start_time = time.time()

        # 获取AI记忆服务
        ai_memory_service = get_ai_memory_service()

        # 转换extract_types为MemoryType枚举
        from app.models.memory import MemoryType

        extract_types = []
        for type_str in request.extract_types:
            try:
                if type_str.lower() == 'character':
                    extract_types.append(MemoryType.CHARACTER)
                elif type_str.lower() == 'scene':
                    extract_types.append(MemoryType.SCENE)
                elif type_str.lower() == 'plot':
                    extract_types.append(MemoryType.PLOT)
                elif type_str.lower() == 'world':
                    extract_types.append(MemoryType.WORLD)
            except Exception as e:
                print(f"转换类型失败: {type_str} -> {e}")

        # 处理章节内容
        result = ai_memory_service.process_chapter_content(
            project_id=project_id,
            chapter_id=chapter_id,
            content=request.content,
            extract_types=extract_types
        )

        processing_time = time.time() - start_time

        if result.success:
            # 转换记忆数据为字典格式
            extracted_memories = []
            for memory in result.extracted_memories:
                memory_dict = {
                    "id": memory.id,
                    "type": memory.type.value,
                    "title": memory.title,
                    "content": memory.content,
                    "summary": memory.summary,
                    "characters": memory.characters,
                    "scenes": memory.scenes,
                    "tags": memory.tags,
                    "created_at": memory.created_at.isoformat(),
                    "updated_at": memory.updated_at.isoformat()
                }
                extracted_memories.append(memory_dict)

            # 更新章节向量化状态
            chapter_service.update_chapter_vectorization_status(
                project_id,
                chapter_id,
                len(extracted_memories)
            )

            return VectorizeChapterResponse(
                success=True,
                message=f"章节向量化完成，提取了 {len(extracted_memories)} 条记忆",
                extracted_memories=extracted_memories,
                processing_time=processing_time,
                memory_count=len(extracted_memories)
            )
        else:
            return VectorizeChapterResponse(
                success=False,
                message=result.message or "向量化处理失败",
                processing_time=processing_time
            )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"向量化处理失败: {str(e)}"
        )


@router.get("/{chapter_id}/memories", response_model=List[Dict[str, Any]])
async def get_chapter_memories(project_id: str, chapter_id: str):
    """获取章节相关的记忆"""
    try:
        ai_memory_service = get_ai_memory_service()

        # 首先尝试通过章节ID搜索
        search_results = []

        # 方法1: 搜索包含章节ID的记忆
        try:
            search_results = ai_memory_service.search_memories(
                query=chapter_id,
                project_id=project_id,
                limit=50,
                similarity_threshold=0.1  # 降低阈值
            )
        except Exception as e:
            print(f"章节ID搜索失败: {e}")

        # 方法2: 如果没有结果，获取章节内容进行语义搜索
        if not search_results:
            try:
                # 获取章节内容
                chapter = chapter_service.get_chapter(project_id, chapter_id)
                if chapter and chapter.content:
                    # 提取章节内容的关键词进行搜索
                    content_preview = chapter.content[:200]  # 取前200字符
                    search_results = ai_memory_service.search_memories(
                        query=content_preview,
                        project_id=project_id,
                        limit=20,
                        similarity_threshold=0.3
                    )
            except Exception as e:
                print(f"内容搜索失败: {e}")

        # 方法3: 如果还是没有结果，返回项目的所有记忆（限制数量）
        if not search_results:
            try:
                search_results = ai_memory_service.search_memories(
                    query="",  # 空查询返回所有记忆
                    project_id=project_id,
                    limit=10,
                    similarity_threshold=0.0
                )
            except Exception as e:
                print(f"全量搜索失败: {e}")

        print(f"章节 {chapter_id} 找到 {len(search_results)} 条记忆")

        memories = []
        for result in search_results:
            memory_dict = {
                "id": result.memory.id,
                "type": result.memory.type.value,
                "title": result.memory.title,
                "content": result.memory.content,
                "summary": result.memory.summary,
                "characters": result.memory.characters,
                "scenes": result.memory.scenes,
                "tags": result.memory.tags,
                "similarity_score": result.similarity_score,
                "created_at": result.memory.created_at.isoformat(),
                "updated_at": result.memory.updated_at.isoformat()
            }
            memories.append(memory_dict)

        return memories

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取章节记忆失败: {str(e)}"
        )


@router.get("/{chapter_id}/vectorization-status", response_model=Dict[str, Any])
async def get_chapter_vectorization_status(project_id: str, chapter_id: str):
    """获取章节向量化状态"""
    try:
        ai_memory_service = get_ai_memory_service()

        # 获取章节相关的记忆统计
        search_results = ai_memory_service.search_memories(
            query=f"chapter:{chapter_id}",
            project_id=project_id,
            limit=1
        )

        has_memories = len(search_results) > 0

        # 获取记忆统计
        stats = ai_memory_service.get_enhanced_memory_stats(project_id)

        return {
            "vectorized": has_memories,
            "memory_count": len(search_results) if has_memories else 0,
            "last_updated": search_results[0].memory.updated_at.isoformat() if has_memories else None,
            "project_stats": stats
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取向量化状态失败: {str(e)}"
        )


@router.get("/{chapter_id}/characters")
async def get_chapter_characters(project_id: str, chapter_id: str):
    """获取章节的角色信息"""
    try:
        # 获取项目所有角色，然后筛选出现在该章节的角色
        all_characters = character_storage_service.get_project_characters(project_id)
        chapter_characters = [
            char for char in all_characters
            if chapter_id in char.get('appearances', [])
        ]

        return {
            "success": True,
            "characters": chapter_characters,
            "total": len(chapter_characters)
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取章节角色失败: {str(e)}"
        )


@router.get("/characters")
async def get_project_characters(project_id: str):
    """获取项目的所有角色信息"""
    try:
        characters = character_storage_service.get_project_characters(project_id)
        stats = character_storage_service.get_character_statistics(project_id)

        return {
            "success": True,
            "characters": characters,
            "statistics": stats,
            "total": len(characters)
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取项目角色失败: {str(e)}"
        )


@router.get("/characters/export")
async def export_characters_json(project_id: str):
    """导出角色信息为JSON格式"""
    try:
        json_data = character_storage_service.export_characters_json(project_id)

        return {
            "success": True,
            "json_data": json_data,
            "message": "角色信息导出成功"
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"导出角色信息失败: {str(e)}"
        )


@router.get("/characters/{character_name}")
async def get_character_by_name(project_id: str, character_name: str):
    """根据名称获取角色详细信息"""
    try:
        character = character_storage_service.get_character_by_name(project_id, character_name)

        if not character:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"角色 '{character_name}' 不存在"
            )

        return {
            "success": True,
            "character": character
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取角色信息失败: {str(e)}"
        )
